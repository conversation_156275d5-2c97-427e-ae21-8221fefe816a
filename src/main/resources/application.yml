# ========================
# 应用程序基础配置
# ========================
server:
  port: 8081  # 服务器端口号
  # Tomcat连接配置 - 优化文件下载和客户端连接处理
  tomcat:
    connection-timeout: 60000      # 连接超时时间(毫秒) - 60秒
    keep-alive-timeout: 60000      # Keep-Alive超时时间(毫秒) - 60秒
    max-keep-alive-requests: 100   # 最大Keep-Alive请求数
    max-connections: 8192          # 最大连接数
    accept-count: 100              # 等待队列长度
    threads:
      max: 200                     # 最大工作线程数
      min-spare: 10                # 最小空闲线程数
  # HTTP响应配置
  compression:
    enabled: true                  # 启用响应压缩
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024        # 最小压缩响应大小

# ========================
# Thymeleaf 模板引擎配置
# ========================
spring:
  thymeleaf:
    cache: false           # 禁用模板缓存（开发环境建议关闭）
    prefix: classpath:/templates/  # 模板文件路径前缀
    suffix: .html          # 模板文件后缀
    mode: HTML             # 模板解析模式

  # ======================
  # 数据源配置 (MySQL)
  # ======================
  datasource:
    url: ***************************************************************************************************************
    username: root              # 数据库用户名
    password: Hilury157195!     # 数据库密码
    driver-class-name: com.mysql.cj.jdbc.Driver  # JDBC驱动类名

  # ======================
  # JPA 持久化配置
  # ======================
  jpa:
    hibernate:
      ddl-auto: update     # 自动更新数据库结构（update/none）
    show-sql: true         # 显示生成的SQL语句
    properties:
      hibernate:
        format_sql: true   # 格式化SQL输出
    database-platform: org.hibernate.dialect.MySQL8Dialect  # 数据库方言
    open-in-view: false    # 禁用OpenSessionInView

  # ======================
  # 静态资源配置
  # ======================
  web:
    resources:
      static-locations: classpath:/static/  # 静态资源路径
  mvc:
    static-path-pattern: /static/**         # 静态资源访问路径

  # ======================
  # 文件上传配置
  # ======================
  servlet:
    multipart:
      max-file-size: 10MB      # 单个文件最大尺寸
      max-request-size: 10MB   # 请求最大尺寸

  # ======================
  # Redis 配置
  # ======================
  redis:
    host: localhost        # Redis服务器地址
    port: 6379             # Redis端口号
    database: 0            # 数据库索引
    timeout: 10000         # 连接超时时间(毫秒)
    lettuce:
      pool:
        max-active: 8      # 最大活跃连接数
        max-wait: -1       # 最大等待时间(负值表示无限等待)
        max-idle: 8        # 最大空闲连接数
        min-idle: 0        # 最小空闲连接数

  main:
    allow-bean-definition-overriding: true  # 允许Bean定义覆盖

# ========================
# JWT 鉴权配置
# ========================
jwt:
  secret: F9A8C28B7E5D3F1A6E5D4C3B2A1F0E9D8C7B6A5F4E3D2C1B0A9G8H7I6J5K4L3M2N1  # 密钥
  expiration: 86400000    # Token有效期(毫秒)，默认24小时

# ========================
# Maizi Edu 配置
# ========================
maizi:
  dailyUploadStatsEnabled: true

# ========================
# MyBatis-Plus 配置
# ========================
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml               # Mapper文件路径
  type-aliases-package: com.edu.maizi_edu_sys.entity          # 实体类包扫描路径
  configuration:
    map-underscore-to-camel-case: true                        # 开启字段下划线转驼峰
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # SQL日志输出实现类 - 已注释以减少控制台输出
  global-config:
    db-config:
      logic-delete-field: deleted  
      logic-delete-value: 1    
      logic-not-delete-value: 0

# ========================
# 日志系统配置
# ========================
logging:
  level:
    com.baomidou.mybatisplus: INFO                              # MyBatis-Plus日志级别 - 改为INFO减少SQL输出
    com.edu.maizi_edu_sys.repository: INFO                      # DAO层日志级别 - 改为INFO减少查询输出
    com.edu.maizi_edu_sys: INFO                                 # 应用主包日志级别 - 改为INFO
    com.edu.maizi_edu_sys.service.impl.ChatServiceImpl: INFO    # 聊天服务日志级别
    com.edu.maizi_edu_sys.controller: INFO                      # 控制器日志级别
    com.edu.maizi_edu_sys.config.AuthInterceptor: WARN          # Auth拦截器日志级别 - 减少频繁输出
    com.volcengine.ark: INFO                                    # 火山引擎组件日志级别
    org.springframework.web.socket: WARN                        # WebSocket日志级别
    org.springframework.messaging: WARN                         # 消息通信日志级别
    org.springframework.web: INFO                               # Spring Web日志级别
    root: INFO                                                  # 全局默认日志级别
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"  # 控制台输出格式
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"     # 文件输出格式

# ========================
# 用户模块配置
# ========================
user:
  avatar-path: uploads/avatars/            # 头像存储路径
  default-avatar: default-avatar.png       # 默认头像文件名
  max-avatar-size: 5242880                 # 最大头像尺寸(5MB)
  allowed-avatar-types: image/jpeg, image/png, image/gif  # 允许上传的头像类型

# ========================
# 文件上传配置
# ========================
file:
  upload:
    base-path: ./uploads                         # 文件上传根目录
    avatar:
      path: ${file.upload.base-path}/avatars     # 头像上传子目录

# ========================
# 聊天机器人配置
# ========================
bot:
  id: bot-20250507182807-dbmrx                    # 机器人唯一标识
  api-key: 71c264ae-dc70-42b6-84c1-72b055ecfb8c   # API访问密钥

# ========================
# 遗传算法参数配置
# ========================
algorithm:
  genetic:
    # 难度区间阈值配置
    difficulty:
      easy-max: 0.4     # 简单题最大难度值
      medium-max: 0.7   # 中等题最大难度值
      hard-min: 0.71    # 困难题最小难度值
    
    population-size: 300
    max-generations: 200
    min-generations: 50
    crossover-rate: 0.8
    mutation-rate: 0.1
    tournament-size: 5
    early-terminate-threshold: 0.97
    global-timeout-seconds: 20

    # 适应度权重配置
    fitness-weights:
      score: 0.21                     # 分数匹配权重
      quality: 0.20                   # 题目质量权重
      difficulty-distribution: 0.15   # 难度分布权重
      cognitive-distribution: 0.01    # 认知水平分布权重
      kp-coverage: 0.10               # 知识点覆盖权重
      topic-type-diversity: 0.05      # 题型多样性权重
      kp-type-balance: 0.03           # 知识点题型均衡权重
      tag-diversity: 0.25             # 标签多样性权重

    # 修复算子配置
    repair:
      enabled: true
      max-steps: 3
      greedy-threshold: 0.1

    # 自适应变异配置
    adaptive-mutation:
      enabled: true
      max-rate: 0.3
      min-rate: 0.05
      stagnation-threshold: 5

  # 内存管理配置
  memory:
    bitset-pool-size: 100
    bitset-size: 10000
    gc-frequency: 50
    memory-threshold: 0.8

  # 监控配置
  monitoring:
    enabled: true
    log-level: INFO
    metrics-collection: true
    fitness-tracking: true
    convergence-analysis: true
  diversity:
    similarity-threshold: 0.85
    # 知识点级别多样性控制
    knowledge-point-level:
      enabled: true                           # 是否启用知识点级别多样性控制
      min-reuse-interval-days: 1              # 知识点内部最小重用间隔（天）- 降低到1天
      max-topics-per-knowledge-point: 50      # 每个知识点最大题目数量 - 增加到50题
      priority-weight-usage: 10.0             # 使用次数权重
      priority-weight-time: 1.0               # 时间权重
  quality:
    error-rate-weight: 0.6
    freshness-weight: 0.4
  exposure-control:
    default-min-reuse-interval-days: 1

# ========================
# 豆包大模型 API 配置
# ========================
doubao:
  api:
    key: fa68bb2a-84f4-48a6-8100-3c11d60f902a # 优先从环境变量 ARK_API_KEY 读取，如果不存在则为空。请在此处或环境变量中设置您的API Key
    endpoint-id: doubao-seed-1.6-250615 # 请替换为你的模型 Endpoint ID, 例如 doubao-pro-32k

# ========================
# MineRU 文档解析API配置
# ========================
mineru:
  api:
    base-url: https://mineru.net              # MineRU API基础URL
    timeout: 60                               # API请求超时时间(秒)
    max-retries: 3                            # 最大重试次数
    retry-delay: 2000                         # 重试延迟(毫秒)
    
    # 多密钥配置 - 硬编码API密钥用于生产部署
    tokens:
      - key: "eyJ0eXBlIjoiSldUIiwiYWxnIjoiSFM1MTIifQ.eyJqdGkiOiIzMzEwODg1MCIsInJvbCI6IlJPTEVfUkVHSVNURVIiLCJpc3MiOiJPcGVuWExhYiIsImlhdCI6MTc1MzA2MjI5NiwiY2xpZW50SWQiOiJsa3pkeDU3bnZ5MjJqa3BxOXgydyIsInBob25lIjoiMTMwNzczNzY3MTAiLCJvcGVuSWQiOm51bGwsInV1aWQiOiJiYTRhMmNmOC02NzU3LTQyMDQtYjJlOS00YmFiZWQxMWY4OGYiLCJlbWFpbCI6IiIsImV4cCI6MTc1NDI3MTg5Nn0.x6ltEOZHuYzreKa3MqjUNM1-0EBamt54FdTWnJrVayUYLMtrDzB5hDyyF5nLm_0sn8W_kj7FXSx0OdDoaC7FSQ"     # 主要API密钥 (硬编码)
        name: "primary"                                        # 密钥别名
        priority: 1                                            # 优先级 (1=最高)
        daily-limit: 2000                                      # 每日高优先级页数限制
        enabled: true                                          # 是否启用
      - key: "sk-mineru-backup1-key-09876543210987654321"      # 备用API密钥1 (硬编码)
        name: "backup1"
        priority: 2
        daily-limit: 1800
        enabled: true                                         # 启用备用密钥
      - key: "sk-mineru-backup2-key-11111111112222222222"     # 备用API密钥2 (硬编码)
        name: "backup2"
        priority: 3
        daily-limit: 1500
        enabled: true                                         # 启用备用密钥
    
    # 负载均衡策略
    load-balance:
      strategy: "priority"                    # 优先级策略: round-robin, priority, least-used
      failover-enabled: true                  # 启用故障转移
      health-check-interval: 300000           # 健康检查间隔(毫秒) 5分钟
  
  # 默认解析参数
  parse:
    default:
      is-ocr: true                            # 默认启用OCR
      enable-formula: true                    # 默认启用公式识别
      enable-table: true                      # 默认启用表格识别
      language: ch                            # 默认语言 (ch=中文, en=英文, auto=自动识别)
      model-version: v2                       # 模型版本 (v1/v2, 推荐使用v2获得更好效果)
      output-format: ["markdown", "json"]     # 默认输出格式 (markdown和json为默认格式)
      extra-formats: ["docx", "html"]         # 额外输出格式 (可选: docx, html, latex)
    
    # 文件上传限制 (根据官方API限制)
    file:
      max-size: 200MB                         # 单文件最大大小 (官方限制200MB)
      max-pages: 600                          # 最大页数限制 (官方限制600页)
      allowed-types: pdf,doc,docx,ppt,pptx,png,jpg,jpeg  # 官方支持的文件类型
      batch-max-count: 200                    # 批量上传最大文件数 (官方限制200个)
  
  # 任务调度配置
  scheduler:
    enabled: false                            # 是否启用文档解析任务调度器 (暂时关闭，因为缺少数据库表)
    sync-interval: 300000                     # 状态同步间隔(毫秒) - 5分钟
    cleanup-cron: "0 0 2 * * ?"               # 清理任务cron表达式 - 每天凌晨2点
    cleanup-expire-days: 30                   # 清理过期天数
    max-concurrent-sync: 10                   # 最大并发同步任务数
  
  # 回调配置
  callback:
    enabled: true                             # 是否启用回调
    secret: ${MINERU_CALLBACK_SECRET:mineru_callback_2025}  # 回调验证密钥
    timeout: 30                               # 回调超时时间(秒)
  
  # 缓存配置
  cache:
    enabled: true                             # 是否启用缓存
    ttl: 3600                                 # 缓存TTL(秒) - 1小时
    max-size: 1000                            # 最大缓存条目数
  
  # 监控配置
  monitoring:
    enabled: true                             # 是否启用监控
    metrics-interval: 60                      # 指标收集间隔(秒)
    log-slow-requests: true                   # 是否记录慢请求
    slow-request-threshold: 10000             # 慢请求阈值(毫秒)

# ========================
# 用户统计系统配置
# ========================
user-stats:
  # 内存安全模式配置
  memory-safe-mode:
    enabled: true                    # 启用内存安全模式
    memory-threshold: 0.85           # 内存使用率阈值（超过此值启用简化计算）
    max-query-limit: 5000            # 单次查询最大记录数
    ranking-calculation: simplified  # 排名计算模式: full, simplified, disabled

  # 缓存配置
  cache:
    enabled: true                    # 启用统计数据缓存
    expire-minutes: 30               # 缓存过期时间（分钟）
    max-size: 1000                   # 最大缓存条目数
    auto-refresh: true               # 自动刷新活跃用户缓存
    refresh-interval-minutes: 30     # 缓存刷新间隔（分钟）

# ========================
# 题目校对系统配置
# ========================
correction:
  # 系统总开关
  enabled: true               # 重要：在服务器上必须禁用以防止CPU过载
  # 运行配置
  runner:
    interval: PT1M            # 调度间隔，PT1M表示1分钟，PT30S表示30秒

  # 批处理配置
  batch:
    size: 3                   # 每批处理题目数量

  # 线程池配置
  executor:
    core-size: 1
    max-size: 1
    queue-capacity: 6

  # API配置
  api:
    timeout-seconds: 120       # AI API调用超时时间（秒）
    retry-delay-seconds: 30    # 重试间隔时间

  # 审核配置
  approval:
    enabled: true              # 是否启用人工审核模式（true=需人工审核，false=自动应用）
    auto-reject-hours: 1440    # 自动拒绝超时时间（小时）

  # 时间调度配置
  schedule:
    enabled: false             # 是否启用时间限制（true=限定时间窗口，false=任何时间都可运行）
    start-time: "02:00"        # 允许自动更新的开始时间（24小时制）
    end-time: "06:00"          # 允许自动更新的结束时间（24小时制）
