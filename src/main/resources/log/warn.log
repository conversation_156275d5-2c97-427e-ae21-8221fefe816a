2025-07-30 09:00:22.366 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:00:22.879 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.883 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.887 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:00:22.913 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:00:22.916 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:00:22.932 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.933 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.934 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.937 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:00:23.051 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.052 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.052 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.052 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.053 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.053 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.053 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.054 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:26.065 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:26.188 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:00:26.188 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:01:10.916 [http-nio-8081-exec-8] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-28T06:54:04Z. Current time: 2025-07-30T01:01:10Z, a difference of 151626914 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.***************************************************************.FNpQ8qHYlKA_Wll9tnLxP9D1dKklVNEO1_kUtlSZdY1GgQAGRdJlyJujUabrD2BftEnLf5eKVPBJdEcx0JSQJw]
2025-07-30 09:01:10.917 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/user/info
2025-07-30 09:01:10.924 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/page
2025-07-30 09:01:11.097 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/statistics
2025-07-30 09:01:11.128 [http-nio-8081-exec-7] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /user/info
2025-07-30 09:01:11.193 [http-nio-8081-exec-6] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/users/current
2025-07-30 09:03:31.410 [http-nio-8081-exec-2] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-28T06:54:04Z. Current time: 2025-07-30T01:03:31Z, a difference of 151767410 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.***************************************************************.FNpQ8qHYlKA_Wll9tnLxP9D1dKklVNEO1_kUtlSZdY1GgQAGRdJlyJujUabrD2BftEnLf5eKVPBJdEcx0JSQJw]
2025-07-30 09:03:31.411 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/user/info
2025-07-30 09:03:31.448 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/page
2025-07-30 09:03:31.631 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/statistics
2025-07-30 09:03:31.678 [http-nio-8081-exec-4] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /user/info
2025-07-30 09:03:31.737 [http-nio-8081-exec-10] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/users/current
2025-07-30 09:03:33.955 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/log
2025-07-30 09:06:45.836 [correction-exec(core=1,max=1)-1] ERROR com.edu.maizi_edu_sys.service.TopicCorrectionService - 处理批次时发生错误
java.lang.RuntimeException: AI服务调用异常: null
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:77) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:142) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63dc935a.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_221]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915) ~[?:1.8.0_221]
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:72) ~[classes/:?]
	... 17 more
2025-07-30 09:06:45.851 [correction-exec(core=1,max=1)-1] WARN  com.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@6aa09b91 marked as broken because of SQLSTATE(08003), ErrorCode(0)
java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1649) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1565) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor134.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy182.prepare(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:87) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor138.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy181.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor141.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy125.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy151.update(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:265) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63dc935a.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1580) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	... 52 more
2025-07-30 09:06:56.532 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:06:56.965 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:56.966 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:56.969 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:06:56.990 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:06:56.992 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:06:57.006 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.007 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.007 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.010 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:06:57.102 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.102 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.102 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.103 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.103 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.103 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.104 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.104 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:59.721 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:59.837 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:06:59.838 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:07:45.664 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:07:46.041 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.041 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.045 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:07:46.059 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:07:46.061 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:07:46.071 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.071 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.071 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.074 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:07:46.163 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.164 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.164 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.164 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.165 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.165 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.165 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.166 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:48.722 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:48.833 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:07:48.834 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:13:52.337 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:13:52.786 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.786 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.790 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:13:52.806 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:13:52.808 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:13:52.822 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.822 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.823 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.827 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:13:52.911 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.912 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.912 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.912 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.913 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.913 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.913 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.914 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:55.997 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:56.101 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:13:56.102 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:13:57.746 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-07-30 09:13:57.812 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-30 09:14:01.230 [correction-exec(core=1,max=1)-1] ERROR com.edu.maizi_edu_sys.service.TopicCorrectionService - 处理批次时发生错误
java.lang.RuntimeException: AI服务调用异常: null
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:77) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:142) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_221]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915) ~[?:1.8.0_221]
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:72) ~[classes/:?]
	... 17 more
2025-07-30 09:14:01.244 [correction-exec(core=1,max=1)-1] WARN  com.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@e3794ec marked as broken because of SQLSTATE(08003), ErrorCode(0)
java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1649) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1565) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor134.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy182.prepare(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:87) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor137.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy181.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor139.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy125.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy151.update(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:265) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1580) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	... 52 more
2025-07-30 09:14:09.038 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:14:09.465 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.465 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.469 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:14:09.483 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:14:09.485 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:14:09.495 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.495 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.495 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.498 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:14:09.574 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.575 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.575 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.575 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.575 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.576 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.576 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.576 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:12.461 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:12.582 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:14:12.583 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:18:10.126 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:18:10.654 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.655 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.660 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:18:10.679 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:18:10.682 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:18:10.699 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.700 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.701 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.705 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:18:10.849 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.850 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.851 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.852 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.852 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.853 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.853 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.853 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:14.869 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:15.024 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:18:15.026 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:18:46.380 [http-nio-8081-exec-6] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /admin/correction-approval/statistics
2025-07-30 09:19:35.765 [correction-exec(core=1,max=1)-1] ERROR com.edu.maizi_edu_sys.service.TopicCorrectionService - 处理批次时发生错误
java.lang.RuntimeException: AI服务调用异常: null
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:77) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:142) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_221]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915) ~[?:1.8.0_221]
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:72) ~[classes/:?]
	... 17 more
2025-07-30 09:19:35.783 [correction-exec(core=1,max=1)-1] WARN  com.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@13128a6 marked as broken because of SQLSTATE(08003), ErrorCode(0)
java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1649) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1565) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor139.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy182.prepare(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:87) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy181.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy125.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy151.update(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:265) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1580) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	... 54 more
2025-07-30 09:25:27.655 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:25:28.152 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.153 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.158 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:25:28.176 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:25:28.178 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:25:28.190 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.190 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.191 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.195 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:25:28.318 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.319 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.319 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.319 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.320 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.320 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.320 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.321 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:32.074 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:32.176 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:25:32.177 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:27:49.357 [correction-exec(core=1,max=1)-1] ERROR com.edu.maizi_edu_sys.service.TopicCorrectionService - 处理批次时发生错误
java.lang.RuntimeException: AI服务调用异常: null
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:77) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:142) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_221]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915) ~[?:1.8.0_221]
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:72) ~[classes/:?]
	... 17 more
2025-07-30 09:27:49.379 [correction-exec(core=1,max=1)-1] WARN  com.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@4c140b22 marked as broken because of SQLSTATE(08003), ErrorCode(0)
java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1649) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1565) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor134.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy182.prepare(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:87) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor140.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy181.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor139.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy125.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy151.update(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:265) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1580) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	... 52 more
2025-07-30 09:27:56.789 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:27:57.404 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.404 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.408 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:27:57.429 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:27:57.431 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:27:57.445 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.446 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.446 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.451 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:27:57.545 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.545 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.545 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.546 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.546 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.546 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.547 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.547 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:28:00.643 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:28:00.749 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:28:00.749 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:28:19.609 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户排名计算已简化，避免内存溢出。用户1920280447393230872上传量: 20
2025-07-30 09:29:38.775 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户排名计算已简化，避免内存溢出。用户1920280447393230872上传量: 50
2025-07-30 09:30:39.069 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户排名计算已简化，避免内存溢出。用户1920280447393230872上传量: 80
2025-07-30 09:31:08.842 [http-nio-8081-exec-7] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /admin/correction-approval/statistics
2025-07-30 09:31:53.133 [http-nio-8081-exec-7] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /admin/correction-approval/statistics
2025-07-30 09:32:25.912 [correction-exec(core=1,max=1)-1] ERROR com.edu.maizi_edu_sys.service.TopicCorrectionService - 处理批次时发生错误
java.lang.RuntimeException: AI服务调用异常: null
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:77) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:142) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_221]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915) ~[?:1.8.0_221]
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:72) ~[classes/:?]
	... 17 more
2025-07-30 09:32:25.927 [correction-exec(core=1,max=1)-1] WARN  com.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@4965fb52 marked as broken because of SQLSTATE(08003), ErrorCode(0)
java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1649) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1565) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor136.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy182.prepare(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:87) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor200.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy181.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor225.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy125.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy151.update(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:265) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1580) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	... 52 more
2025-07-30 09:49:36.406 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:49:36.843 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.843 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.847 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:49:36.863 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:49:36.865 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:49:36.878 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.879 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.879 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.883 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:49:36.982 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.983 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.983 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.983 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.984 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.984 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.984 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.984 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:40.167 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:40.281 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:49:40.282 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:49:44.141 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-30 09:49:44.141 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-30 09:49:44.141 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-30 09:49:44.141 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-30 09:49:44.141 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-30 09:49:44.141 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-30 09:49:47.401 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户排名计算已简化，避免内存溢出。用户1920280447393230872上传量: 80
2025-07-30 09:49:49.047 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-30 09:49:49.048 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-30 09:49:49.048 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-30 09:49:49.048 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-30 09:49:49.048 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-30 09:49:49.048 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-30 09:50:13.100 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-30 09:50:13.100 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-30 09:50:13.100 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-30 09:50:13.102 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-30 09:50:13.102 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-30 09:50:13.102 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-30 09:50:14.122 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-30 09:50:14.122 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-30 09:50:14.122 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-30 09:50:14.123 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-30 09:50:14.122 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-30 09:50:14.123 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-30 09:50:14.682 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor
2025-07-30 09:53:30.173 [http-nio-8081-exec-6] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /admin/correction-approval/statistics
2025-07-30 09:58:47.150 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:58:47.973 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:47.974 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:47.981 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:58:48.011 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:58:48.015 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:58:48.034 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.035 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.037 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.043 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:58:48.201 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.202 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.203 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.204 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.205 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.205 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.206 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.207 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:50.073 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:52.708 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:58:52.710 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:58:56.375 [main] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:829) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.selectOne(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.getConfigValue(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.getConfigValue(SystemConfigServiceImpl.java:178) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.initDefaultConfigs(SystemConfigServiceImpl.java:273) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.init(SystemConfigServiceImpl.java:172) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:24) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[app.jar:?]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88) ~[app.jar:?]
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	... 103 more
Caused by: java.net.UnknownHostException: db
	at java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method) ~[?:1.8.0_221]
	at java.net.InetAddress$2.lookupAllHostAddr(InetAddress.java:929) ~[?:1.8.0_221]
	at java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1324) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName0(InetAddress.java:1277) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1193) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1127) ~[?:1.8.0_221]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:133) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	... 103 more
2025-07-30 09:58:56.389 [main] ERROR com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - ��ȡ����ֵʧ��: topic_correction_prompt
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/edu/maizi_edu_sys/mapper/SystemConfigMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.SystemConfigMapper.getConfigValue
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.selectOne(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.getConfigValue(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.getConfigValue(SystemConfigServiceImpl.java:178) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.initDefaultConfigs(SystemConfigServiceImpl.java:273) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.init(SystemConfigServiceImpl.java:172) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:24) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[app.jar:?]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88) ~[app.jar:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/edu/maizi_edu_sys/mapper/SystemConfigMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.SystemConfigMapper.getConfigValue
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:829) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: java.net.UnknownHostException: db
	at java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method) ~[?:1.8.0_221]
	at java.net.InetAddress$2.lookupAllHostAddr(InetAddress.java:929) ~[?:1.8.0_221]
	at java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1324) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName0(InetAddress.java:1277) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1193) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1127) ~[?:1.8.0_221]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:133) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
2025-07-30 09:58:57.411 [main] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:829) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.initDefaultConfigs(SystemConfigServiceImpl.java:274) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.init(SystemConfigServiceImpl.java:172) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:24) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[app.jar:?]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88) ~[app.jar:?]
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	... 104 more
Caused by: java.net.UnknownHostException: db
	at java.net.InetAddress.getAllByName0(InetAddress.java:1281) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1193) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1127) ~[?:1.8.0_221]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:133) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	... 104 more
2025-07-30 09:58:57.419 [main] ERROR com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - ��������ʧ��: topic_correction_prompt = ����һλ�����Ľ���������������ר�ң������ǳ䵱�Զ���У��ϵͳ��������ݿ��еĽ�����Ŀ��

��ĺ���Ŀ�����ҳ���Ŀ�е��κδ��󣬲������ϸ��JSON��ʽ������Ҫ���������ݣ��Ա�������Զ��������ݿ⡣

## ���������������ʽ�淶

�㽫�յ�һ�����������Ŀ��JSON���顣����ϸ���ÿһ���⡣

### 1. ������ִ���

�� **����** ����һ��JSON���飬���а���һ����������ÿ���������һ����Ҫ��������Ŀ��
ÿ������ **����** ������������

- **"id"**: ��Ŀ������ID�����ڶ�λ���ݿ��¼
- **"reason"**: һ���ַ�������һ�仰�����ظ����������޸ĵĺ���ԭ�����磺"ԭ�𰸴���"��"������׼ȷ"��"����д����"��"��Ŀ��������"
- **"updates"**: һ���������� **ֻ����** ��Ҫ���޸ĵ��ֶκ����ǵ���ֵ�����Բ�Ҫ����û�иĶ����ֶ�

**��Ҫ**��updates �����пɰ������ֶ�Ϊ: `type`, `title`, `options`, `subs`, `answer`, `parse`, `difficulty`

?? **�ر�ע�� `options` / `subs` �ĸ�ʽ**��
- �������ֶ����ջ��� JSON �ַ�����ʽ�������ݿ⣬�� **���÷��� updates ��Ӧֱ���ṩ�Ϸ��� JSON ����/����**��ϵͳ���Զ����л���
- ��ȷʾ����`"options": [{"key":"A","name":"ѡ��A"},{"key":"B","name":"ѡ��B"}]`

**�� updates ����Ϊ�գ���û���ֶ���Ҫ�޸ģ����벻Ҫ���ظ��⡣**

### ���ʾ����

```json
[
  {
    "id": 275735,
    "reason": "��ѡ�����ĸ˳������ҽ���������ϸ",
    "updates": {
      "answer": "ACD",
      "options": [{"key":"A","name":"��ȷѡ��A"},{"key":"B","name":"����ѡ��B"},{"key":"C","name":"��ȷѡ��C"},{"key":"D","name":"��ȷѡ��D"}],
      "parse": "��������Ľ�����A����ȷ����Ϣ���м�ֵ����ԣ����ֵ���ˡ���ʱ����ض��졣B�������Ϣ��Ȼ���Թ������ڴ��������п��ܻ���Ļ�ʧ�档C����ȷ����Ϣ����ʱЧ�ԣ�������ʱ�����ƶ�ʧȥ�򽵵ͼ�ֵ��D����ȷ����Ϣ�������������ԣ�����ͨ��ĳ����ʽ�����������ء�"
    }
  }
]
```

**ʧ��ʾ��������д��������ģ�£�**��

```json
{
  "id": 1,
  "reason": "ʾ��"
}
```

### 2. ���û�з����κδ���

��������������Ŀ��������ȱ���� **����** ����һ���յ�JSON���� `[]`��

## ��������嵥

### �������߼�������

#### ��� (title) ��飺
- �Ƿ��д���֡��ﲡ�������Ŵ���
- �����Ƿ�������ȷ����������
- ���ʷ�ʽ�Ƿ�ǡ����"�����ĸ�"��"����...����˵����ȷ����"�ȣ�
- �Ƿ��������Ҫ���ظ��ʻ�

#### ѡ�� (options) ��飺
- ÿ��ѡ���Ƿ��д���ֻ��﷨����
- ѡ����Ƿ����У������Ƿ���
- ��ѡ�⣺�Ƿ�ֻ��һ����ȷ��ȷ��
- ��ѡ�⣺�Ƿ���2-4����ȷѡ���ѡ���û���ظ���ì��
- ѡ���Ƿ���к�����Ի��ԣ�����ѡ��Ӧ�ÿ��ƺ���
- options��ʽ����������Ч��JSON�����ʽ
- **ѡ����𰸱���ƥ��**������ĸ����ȫ�������� options.key �У�

#### �� (answer) ��飺
- ���Ƿ���ȷ��Ӧ���������
- ������������Ƿ���ȫһ��
- ��ʽ�淶��
  - ��ѡ�� (choice)�������ǵ�����д��ĸ���� "A"��"B"��"C"��"D"������ "E" **���ȱ���Ϊ 1**
  - ��ѡ�� (multiple)�������Ǵ�д��ĸ�� **����ĸ��������**���� "ACD"�������� "CAD"����**��ȷѡ������ 2-5 ��**
  - �ж��� (judge)�������� "��" �� "��"������ʹ������ȫ���ַ���

#### ���� (parse) ��飺
- �Ƿ�������������ȷ��Ϊʲô��ȷ
- �Ƿ�����˴���ѡ��Ϊʲô����
- ���õ�֪ʶ�㡢��ʵ�������Ƿ�׼ȷ
- �����Ƿ�רҵ���꾡���ܰ���ѧ�����
- ��������ظ��𰸣�Ҫ�н�����ֵ

### ��ʽ��淶���

#### ��Ŀ���� (type) ��飺
- **choice**����ѡ�⣬��Ϊ������ĸ
- **multiple**����ѡ�⣬��Ϊ�����ĸ
- **judge**���ж��⣬��Ϊ"��"��"��"
- type�Ƿ���ʵ����Ŀ��ʽƥ��

#### �Ѷ� (difficulty) ������
- ȡֵ��Χ��0.1 - 1.0��**����һλС��**��
- �ο���׼��
  - 0.1-0.3�����������⣬ֱ�Ӽ�����
  - 0.4-0.6�����Ӧ���⣬��Ҫ˼������
  - 0.7-0.9���ۺϷ����⣬��Ҫ������
  - 1.0�������⣬��Ҫ����˼ά

### ������������ʾ��

1. **�𰸸�ʽ����**��
   - ��ѡ��� "CAD" �� "ACD"
   - ��ѡ��� "ab" �� "A"

2. **���Ͳ�ƥ��**��
   - ���� "AC" �� type �� "choice" �� type ӦΪ "multiple"

3. **������������**��
   - "A��ȷ��B����C����D����" �� ��ϸ����ÿ��ѡ���ԭ��

4. **�߼���һ��**��
   - ����˵A��ȷ��������B �� �𰸺ͽ�������һ��

### ��Ҫ����

**���Բ����޸ĵ��ֶ�**��`id`, `know_id`, `tags`, `source`, `score`, `sort`, `created_at`
���������������Ŀ���ݱ���������Ԫ���ݷ��ࡣ

### ������׼

ÿ����Ŀ������Ӧ�ﵽ��
- ����׼ȷ���󣬷���ѧ��֪ʶ
- ��ʽ�淶ͳһ������ϵͳ����
- ������ϸרҵ�����н�����ֵ
- �Ѷ����ú���ƥ����Ŀ���Ӷ�

---
?? **��������Ǵ� JSON**���ش���ֻ�ܰ����Ϸ��� JSON ���飬**ǰ�󲻿ɼд��κν��������֡�Markdown��������ǻ������ַ�**��

���ڣ�������������й�����ϸ�������������Ŀ��
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/edu/maizi_edu_sys/mapper/SystemConfigMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.SystemConfigMapper.insertOrUpdateConfig
### The error occurred while executing an update
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.initDefaultConfigs(SystemConfigServiceImpl.java:274) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.init(SystemConfigServiceImpl.java:172) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:24) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[app.jar:?]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88) ~[app.jar:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/edu/maizi_edu_sys/mapper/SystemConfigMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.SystemConfigMapper.insertOrUpdateConfig
### The error occurred while executing an update
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:196) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:829) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: java.net.UnknownHostException: db
	at java.net.InetAddress.getAllByName0(InetAddress.java:1281) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1193) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1127) ~[?:1.8.0_221]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:133) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
2025-07-30 09:58:57.441 [main] ERROR com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - ��ʼ��Ĭ������ʧ��
java.lang.RuntimeException: ��������ʧ��
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:199) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.initDefaultConfigs(SystemConfigServiceImpl.java:274) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.init(SystemConfigServiceImpl.java:172) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:24) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[app.jar:?]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88) ~[app.jar:?]
Caused by: org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/edu/maizi_edu_sys/mapper/SystemConfigMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.SystemConfigMapper.insertOrUpdateConfig
### The error occurred while executing an update
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	... 63 more
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/edu/maizi_edu_sys/mapper/SystemConfigMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.SystemConfigMapper.insertOrUpdateConfig
### The error occurred while executing an update
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:196) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	... 63 more
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	... 63 more
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:829) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	... 63 more
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	... 63 more
Caused by: java.net.UnknownHostException: db
	at java.net.InetAddress.getAllByName0(InetAddress.java:1281) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1193) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1127) ~[?:1.8.0_221]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:133) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	... 63 more
2025-07-30 09:58:59.669 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-07-30 09:58:59.755 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-30 10:00:31.736 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 10:00:32.171 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:32.172 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:32.175 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 10:00:32.192 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 10:00:32.194 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 10:00:32.205 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:32.206 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:32.206 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:32.211 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 10:00:32.315 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:32.315 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:32.316 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:32.316 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:32.317 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:32.317 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:32.317 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:32.317 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:35.324 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 10:00:35.425 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 10:00:35.425 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 10:00:46.792 [http-nio-8081-exec-5] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /admin/correction-approval/statistics
2025-07-30 10:01:14.100 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户排名计算已简化，避免内存溢出。用户1920280447393230872上传量: 80
2025-07-30 10:04:43.837 [correction-exec(core=1,max=1)-1] ERROR com.edu.maizi_edu_sys.service.TopicCorrectionService - 处理批次时发生错误
java.lang.RuntimeException: AI服务调用异常: null
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:77) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:142) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_221]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915) ~[?:1.8.0_221]
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:72) ~[classes/:?]
	... 17 more
2025-07-30 10:04:43.852 [correction-exec(core=1,max=1)-1] WARN  com.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@194585d7 marked as broken because of SQLSTATE(08003), ErrorCode(0)
java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1649) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1565) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor142.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy182.prepare(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:87) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor219.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy181.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor218.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy125.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy151.update(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:265) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1580) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	... 52 more
