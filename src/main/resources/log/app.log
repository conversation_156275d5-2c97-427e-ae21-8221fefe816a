2025-07-30 09:00:19.804 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:00:19.854 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 12176 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:00:19.870 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:00:21.233 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:00:21.236 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:00:21.304 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:00:21.305 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 55 ms. Found 0 Redis repository interfaces.
2025-07-30 09:00:21.940 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:00:21.948 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:00:21.949 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:00:21.949 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:00:22.178 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:00:22.179 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2221 ms
2025-07-30 09:00:22.335 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:00:22.336 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:00:22.337 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:00:22.339 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:00:22.366 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:00:22.479 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:00:22.879 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.883 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.887 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:00:22.913 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:00:22.916 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:00:22.932 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.933 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.934 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:22.937 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:00:23.051 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.052 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.052 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.052 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.053 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.053 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.053 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:23.054 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:24.871 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:00:24.875 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:00:25.896 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:00:25.982 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:00:26.065 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:00:26.188 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:00:26.188 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:00:26.272 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 3
2025-07-30 09:00:26.323 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:00:26.604 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 09:00:26.743 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:00:26.788 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-30 09:00:27.260 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:00:27.282 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:00:27.719 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:00:27.769 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:00:27.787 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-30 09:00:27.789 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-30 09:00:27.789 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@74e4be21]]
2025-07-30 09:00:27.790 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-30 09:00:27.803 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 8.54 seconds (JVM running for 10.506)
2025-07-30 09:00:27.807 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 1 分钟，批大小 3
2025-07-30 09:00:27.840 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377706
2025-07-30 09:00:27.856 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:00:29.776 [RMI TCP Connection(3)-***********] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:00:29.777 [RMI TCP Connection(3)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:00:29.780 [RMI TCP Connection(3)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-30 09:01:09.929 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-07-30 09:01:10.916 [http-nio-8081-exec-8] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-28T06:54:04Z. Current time: 2025-07-30T01:01:10Z, a difference of 151626914 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTM1OTkyNDQsImV4cCI6MTc1MzY4NTY0NH0.FNpQ8qHYlKA_Wll9tnLxP9D1dKklVNEO1_kUtlSZdY1GgQAGRdJlyJujUabrD2BftEnLf5eKVPBJdEcx0JSQJw]
2025-07-30 09:01:10.917 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/user/info
2025-07-30 09:01:10.924 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/page
2025-07-30 09:01:11.097 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/statistics
2025-07-30 09:01:11.128 [http-nio-8081-exec-7] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /user/info
2025-07-30 09:01:11.193 [http-nio-8081-exec-6] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/users/current
2025-07-30 09:01:13.040 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 372
2025-07-30 09:01:13.045 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 372
2025-07-30 09:01:13.045 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 1 项修正建议，其中 1 项有效
2025-07-30 09:01:13.073 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2341, 日期: 2025-07-30, 过期时间: 2025-09-28 09:01:13
2025-07-30 09:01:13.074 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 1 条修正建议待审核，审核ID: 2341
2025-07-30 09:01:13.077 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377709
2025-07-30 09:01:13.080 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377709的重试计数
2025-07-30 09:01:13.109 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:01:27.820 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377709
2025-07-30 09:01:27.822 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:01:49.460 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:01:49.460 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:01:49.460 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:01:49.460 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:01:49.461 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377712
2025-07-30 09:01:49.462 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377712的重试计数
2025-07-30 09:01:49.472 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:02:27.831 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377712
2025-07-30 09:02:27.833 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:02:55.210 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:02:55.210 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:02:55.210 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:02:55.210 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:02:55.212 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377715
2025-07-30 09:02:55.213 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377715的重试计数
2025-07-30 09:02:55.223 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:03:27.844 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377715
2025-07-30 09:03:27.846 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:03:31.163 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-07-30 09:03:31.410 [http-nio-8081-exec-2] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Expired JWT: JWT expired at 2025-07-28T06:54:04Z. Current time: 2025-07-30T01:03:31Z, a difference of 151767410 milliseconds.  Allowed clock skew: 0 milliseconds.. Token: [eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzeHEiLCJpYXQiOjE3NTM1OTkyNDQsImV4cCI6MTc1MzY4NTY0NH0.FNpQ8qHYlKA_Wll9tnLxP9D1dKklVNEO1_kUtlSZdY1GgQAGRdJlyJujUabrD2BftEnLf5eKVPBJdEcx0JSQJw]
2025-07-30 09:03:31.411 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/user/info
2025-07-30 09:03:31.448 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/page
2025-07-30 09:03:31.631 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/statistics
2025-07-30 09:03:31.678 [http-nio-8081-exec-4] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /user/info
2025-07-30 09:03:31.737 [http-nio-8081-exec-10] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/users/current
2025-07-30 09:03:33.955 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/activities/log
2025-07-30 09:03:46.477 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:03:46.477 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:03:46.477 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:03:46.477 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:03:46.478 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377718
2025-07-30 09:03:46.479 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377718的重试计数
2025-07-30 09:03:46.489 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:04:27.853 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377718
2025-07-30 09:04:27.855 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:04:44.418 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:04:44.418 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:04:44.418 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:04:44.418 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:04:44.420 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377721
2025-07-30 09:04:44.421 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377721的重试计数
2025-07-30 09:04:44.431 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:05:27.859 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377721
2025-07-30 09:05:27.862 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:05:50.859 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 265
2025-07-30 09:05:50.859 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 265
2025-07-30 09:05:50.860 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 1 项修正建议，其中 1 项有效
2025-07-30 09:05:50.865 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2342, 日期: 2025-07-30, 过期时间: 2025-09-28 09:05:50
2025-07-30 09:05:50.865 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 1 条修正建议待审核，审核ID: 2342
2025-07-30 09:05:50.866 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377724
2025-07-30 09:05:50.867 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377724的重试计数
2025-07-30 09:05:50.878 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:06:27.861 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377724
2025-07-30 09:06:27.863 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:06:44.916 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-07-30 09:06:44.916 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@74e4be21]]
2025-07-30 09:06:44.916 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-07-30 09:06:45.836 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-30 09:06:45.837 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:06:45.840 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 09:06:45.836 [correction-exec(core=1,max=1)-1] ERROR com.edu.maizi_edu_sys.service.TopicCorrectionService - 处理批次时发生错误
java.lang.RuntimeException: AI服务调用异常: null
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:77) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:142) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63dc935a.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_221]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915) ~[?:1.8.0_221]
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:72) ~[classes/:?]
	... 17 more
2025-07-30 09:06:45.850 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-30 09:06:45.851 [correction-exec(core=1,max=1)-1] WARN  com.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@6aa09b91 marked as broken because of SQLSTATE(08003), ErrorCode(0)
java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1649) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1565) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor134.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy182.prepare(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:87) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor138.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy181.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor141.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy125.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy151.update(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:265) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63dc935a.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1580) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	... 52 more
2025-07-30 09:06:54.344 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:06:54.380 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 2576 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:06:54.409 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:06:55.508 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:06:55.510 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:06:55.560 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:06:55.561 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-07-30 09:06:56.117 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:06:56.124 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:06:56.124 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:06:56.124 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:06:56.352 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:06:56.352 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1904 ms
2025-07-30 09:06:56.505 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:06:56.506 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:06:56.507 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:06:56.509 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:06:56.532 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:06:56.626 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:06:56.965 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:56.966 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:56.969 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:06:56.990 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:06:56.992 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:06:57.006 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.007 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.007 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.010 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:06:57.102 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.102 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.102 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.103 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.103 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.103 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.104 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:57.104 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:58.698 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:06:58.700 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:06:59.574 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:06:59.654 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:06:59.721 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:06:59.837 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:06:59.838 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:06:59.893 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 3
2025-07-30 09:06:59.922 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:07:00.081 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 09:07:00.347 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:07:00.374 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-30 09:07:00.598 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:07:00.619 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:07:01.016 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:07:01.064 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:07:01.078 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-30 09:07:01.080 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-30 09:07:01.080 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@77d54a41]]
2025-07-30 09:07:01.081 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-30 09:07:01.092 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 7.111 seconds (JVM running for 8.904)
2025-07-30 09:07:01.097 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 1 分钟，批大小 3
2025-07-30 09:07:01.114 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377724
2025-07-30 09:07:01.119 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:07:02.246 [RMI TCP Connection(1)-***********] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:07:02.246 [RMI TCP Connection(1)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:07:02.249 [RMI TCP Connection(1)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-30 09:07:24.078 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:07:24.078 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:07:24.078 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:07:24.078 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:07:24.080 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377727
2025-07-30 09:07:24.081 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377727的重试计数
2025-07-30 09:07:24.111 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:07:34.653 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-07-30 09:07:34.653 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@77d54a41]]
2025-07-30 09:07:34.653 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-07-30 09:07:35.610 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-30 09:07:35.611 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:07:35.614 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 09:07:35.623 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-30 09:07:43.704 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:07:43.768 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 17704 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:07:43.771 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:07:44.709 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:07:44.711 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:07:44.759 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:07:44.760 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-07-30 09:07:45.272 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:07:45.279 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:07:45.279 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:07:45.279 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:07:45.495 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:07:45.496 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1689 ms
2025-07-30 09:07:45.638 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:07:45.639 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:07:45.639 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:07:45.642 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:07:45.664 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:07:45.754 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:07:46.041 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.041 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.045 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:07:46.059 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:07:46.061 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:07:46.071 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.071 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.071 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.074 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:07:46.163 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.164 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.164 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.164 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.165 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.165 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.165 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:46.166 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:47.732 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:07:47.735 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:07:48.573 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:07:48.655 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:07:48.722 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:07:48.833 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:07:48.834 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:07:48.891 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 3
2025-07-30 09:07:48.922 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:07:49.212 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 09:07:49.318 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:07:49.346 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-30 09:07:49.577 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:07:49.597 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:07:49.984 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:07:50.030 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:07:50.045 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-30 09:07:50.047 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-30 09:07:50.047 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5600a278]]
2025-07-30 09:07:50.047 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-30 09:07:50.059 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 6.712 seconds (JVM running for 8.478)
2025-07-30 09:07:50.063 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 1 分钟，批大小 3
2025-07-30 09:07:50.081 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377727
2025-07-30 09:07:50.086 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:07:51.864 [RMI TCP Connection(2)-***********] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:07:51.865 [RMI TCP Connection(2)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:07:51.867 [RMI TCP Connection(2)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-30 09:08:28.797 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 160
2025-07-30 09:08:28.800 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 160
2025-07-30 09:08:28.802 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 1 项修正建议，其中 1 项有效
2025-07-30 09:08:28.820 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2343, 日期: 2025-07-30, 过期时间: 2025-09-28 09:08:28
2025-07-30 09:08:28.821 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 1 条修正建议待审核，审核ID: 2343
2025-07-30 09:08:28.822 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377730
2025-07-30 09:08:28.823 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377730的重试计数
2025-07-30 09:08:28.851 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:08:50.078 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377730
2025-07-30 09:08:50.081 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:09:18.067 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:09:18.067 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:09:18.067 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:09:18.068 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:09:18.072 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377733
2025-07-30 09:09:18.074 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377733的重试计数
2025-07-30 09:09:18.098 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:09:50.099 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377733
2025-07-30 09:09:50.106 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:10:06.921 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:10:06.921 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:10:06.921 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:10:06.921 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:10:06.923 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377736
2025-07-30 09:10:06.925 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377736的重试计数
2025-07-30 09:10:06.938 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:10:50.099 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377736
2025-07-30 09:10:50.102 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:11:06.805 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:11:06.805 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:11:06.805 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:11:06.805 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:11:06.807 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377739
2025-07-30 09:11:06.809 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377739的重试计数
2025-07-30 09:11:06.821 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:11:50.108 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377739
2025-07-30 09:11:50.109 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:12:19.753 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:12:19.753 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:12:19.753 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:12:19.753 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:12:19.754 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377742
2025-07-30 09:12:19.755 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377742的重试计数
2025-07-30 09:12:19.764 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:12:50.111 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377742
2025-07-30 09:12:50.114 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:13:09.412 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:13:09.412 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:13:09.413 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:13:09.413 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:13:09.414 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377745
2025-07-30 09:13:09.415 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377745的重试计数
2025-07-30 09:13:09.425 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:13:49.562 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:13:49.648 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 14120 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:13:49.652 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:13:50.123 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377745
2025-07-30 09:13:50.125 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:13:50.917 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:13:50.919 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:13:50.972 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:13:50.973 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
2025-07-30 09:13:51.709 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:13:51.729 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:13:51.730 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:13:51.731 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:13:52.129 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:13:52.129 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2433 ms
2025-07-30 09:13:52.305 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:13:52.307 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:13:52.307 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:13:52.310 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:13:52.337 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:13:52.453 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:13:52.786 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.786 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.790 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:13:52.806 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:13:52.808 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:13:52.822 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.822 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.823 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.827 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:13:52.911 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.912 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.912 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.912 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.913 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.913 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.913 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:52.914 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:54.614 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:13:54.618 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:13:55.799 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:13:55.904 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:13:55.997 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:13:56.101 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:13:56.102 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:13:56.160 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 3
2025-07-30 09:13:56.259 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:13:56.633 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 09:13:56.923 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:13:56.952 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-30 09:13:57.206 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:13:57.231 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:13:57.685 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:13:57.738 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:13:57.746 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-07-30 09:13:57.757 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-30 09:13:57.759 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:13:57.762 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 09:13:57.773 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-30 09:13:57.780 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:13:57.780 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-30 09:13:57.783 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8081"]
2025-07-30 09:13:57.783 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8081"]
2025-07-30 09:13:57.794 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 09:13:57.812 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-30 09:14:00.361 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-07-30 09:14:00.361 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5600a278]]
2025-07-30 09:14:00.361 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-07-30 09:14:01.229 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-30 09:14:01.231 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:14:01.233 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 09:14:01.230 [correction-exec(core=1,max=1)-1] ERROR com.edu.maizi_edu_sys.service.TopicCorrectionService - 处理批次时发生错误
java.lang.RuntimeException: AI服务调用异常: null
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:77) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:142) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_221]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915) ~[?:1.8.0_221]
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:72) ~[classes/:?]
	... 17 more
2025-07-30 09:14:01.243 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-30 09:14:01.244 [correction-exec(core=1,max=1)-1] WARN  com.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@e3794ec marked as broken because of SQLSTATE(08003), ErrorCode(0)
java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1649) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1565) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor134.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy182.prepare(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:87) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor137.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy181.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor139.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy125.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy151.update(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:265) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1580) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	... 52 more
2025-07-30 09:14:06.665 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:14:06.745 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 18680 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:14:06.822 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:14:07.998 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:14:08.000 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:14:08.052 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:14:08.053 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
2025-07-30 09:14:08.613 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:14:08.620 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:14:08.621 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:14:08.621 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:14:08.840 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:14:08.841 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1916 ms
2025-07-30 09:14:09.007 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:14:09.009 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:14:09.009 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:14:09.012 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:14:09.038 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:14:09.139 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:14:09.465 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.465 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.469 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:14:09.483 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:14:09.485 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:14:09.495 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.495 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.495 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.498 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:14:09.574 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.575 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.575 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.575 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.575 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.576 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.576 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:09.576 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:11.318 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:14:11.323 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:14:12.298 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:14:12.387 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:14:12.461 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:14:12.582 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:14:12.583 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:14:12.644 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 3
2025-07-30 09:14:12.677 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:14:12.852 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 09:14:13.143 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:14:13.170 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-30 09:14:13.420 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:14:13.443 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:14:13.881 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:14:13.935 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:14:13.967 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-30 09:14:13.969 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-30 09:14:13.969 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5600a278]]
2025-07-30 09:14:13.969 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-30 09:14:13.981 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 7.781 seconds (JVM running for 9.754)
2025-07-30 09:14:13.988 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 1 分钟，批大小 3
2025-07-30 09:14:14.005 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377745
2025-07-30 09:14:14.010 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:14:15.672 [RMI TCP Connection(3)-***********] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:14:15.672 [RMI TCP Connection(3)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:14:15.675 [RMI TCP Connection(3)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-30 09:14:38.683 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:14:38.683 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:14:38.683 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:14:38.683 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:14:38.685 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377748
2025-07-30 09:14:38.687 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377748的重试计数
2025-07-30 09:14:38.723 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:15:14.000 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377748
2025-07-30 09:15:14.002 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:15:32.940 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:15:32.941 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:15:32.941 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:15:32.941 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:15:32.942 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377751
2025-07-30 09:15:32.943 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377751的重试计数
2025-07-30 09:15:32.952 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:16:13.999 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377751
2025-07-30 09:16:14.001 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:16:41.922 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:16:41.923 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:16:41.923 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:16:41.923 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:16:41.924 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377754
2025-07-30 09:16:41.926 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377754的重试计数
2025-07-30 09:16:41.937 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:17:14.008 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377754
2025-07-30 09:17:14.011 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:17:52.914 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 442
2025-07-30 09:17:52.919 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 442
2025-07-30 09:17:52.921 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 2 项修正建议，其中 2 项有效
2025-07-30 09:17:52.942 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2344, 日期: 2025-07-30, 过期时间: 2025-09-28 09:17:52
2025-07-30 09:17:52.943 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 2 条修正建议待审核，审核ID: 2344
2025-07-30 09:17:52.945 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377757
2025-07-30 09:17:52.946 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377757的重试计数
2025-07-30 09:17:52.963 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:17:57.181 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-07-30 09:17:57.182 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5600a278]]
2025-07-30 09:17:57.182 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-07-30 09:17:58.439 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-30 09:17:58.441 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:17:58.444 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 09:17:58.456 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-30 09:18:06.658 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:18:06.752 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 4396 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:18:06.758 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:18:08.634 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:18:08.639 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:18:08.749 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:18:08.750 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 80 ms. Found 0 Redis repository interfaces.
2025-07-30 09:18:09.545 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:18:09.555 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:18:09.556 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:18:09.556 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:18:09.875 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:18:09.876 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3050 ms
2025-07-30 09:18:10.089 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:18:10.091 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:18:10.092 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:18:10.096 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:18:10.126 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:18:10.239 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:18:10.654 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.655 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.660 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:18:10.679 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:18:10.682 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:18:10.699 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.700 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.701 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.705 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:18:10.849 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.850 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.851 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.852 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.852 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.853 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.853 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:10.853 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:13.326 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:18:13.331 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:18:14.596 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:18:14.728 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:18:14.869 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:18:15.024 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:18:15.026 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:18:15.126 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 3
2025-07-30 09:18:15.178 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:18:15.706 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 09:18:15.890 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:18:15.938 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-30 09:18:16.293 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:18:16.327 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:18:16.950 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:18:17.039 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:18:17.076 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-30 09:18:17.079 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-30 09:18:17.079 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5600a278]]
2025-07-30 09:18:17.080 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-30 09:18:17.102 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 11.075 seconds (JVM running for 14.103)
2025-07-30 09:18:17.111 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 1 分钟，批大小 3
2025-07-30 09:18:17.137 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377757
2025-07-30 09:18:17.145 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:18:18.565 [RMI TCP Connection(2)-***********] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:18:18.566 [RMI TCP Connection(2)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:18:18.570 [RMI TCP Connection(2)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-30 09:18:33.340 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Generating token for user: sxq with secret (first 5): 'F9A8C...'
2025-07-30 09:18:44.600 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 获取用户每日审核分组数据 - 页码: 1, 大小: 10, 关键词: 
2025-07-30 09:18:44.845 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 审核表总记录数: 83440
2025-07-30 09:18:44.853 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 审核统计数据: pending=461, approved=82856, rejected=123, total=83440
2025-07-30 09:18:45.309 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - === 开始获取提交者列表 ===
2025-07-30 09:18:45.332 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - topic_audit表总记录数: 83440
2025-07-30 09:18:45.380 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 有user_id的记录数: 83440
2025-07-30 09:18:45.382 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 待审核记录数: 461
2025-07-30 09:18:45.383 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 待审核且有user_id的记录数: 461
2025-07-30 09:18:45.384 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 开始查询待审核提交者信息
2025-07-30 09:18:45.384 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 执行统计查询SQL: (audit_status = #{ew.paramNameValuePairs.MPGENVAL1} AND user_id IS NOT NULL) GROUP BY user_id ORDER BY COUNT(*) DESC
2025-07-30 09:18:45.388 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 获取到 4 个待审核提交者的统计信息
2025-07-30 09:18:45.388 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 统计信息示例: [{user_id=1920280447393230859, count=209}, {user_id=1920280447393230860, count=142}, {user_id=1920280447393230855, count=90}]
2025-07-30 09:18:45.390 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 需要查询的用户ID列表: [1920280447393230859, 1920280447393230860, 1920280447393230855, 1920280447393230872]
2025-07-30 09:18:45.395 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 获取到 4 个用户的详细信息
2025-07-30 09:18:45.396 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 用户信息示例: [{id=1920280447393230859, username=王女杰}, {id=1920280447393230860, username=吴宇恒}, {id=1920280447393230855, username=adminaa}]
2025-07-30 09:18:45.397 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 合并后获取到 4 个有效的提交者信息
2025-07-30 09:18:45.397 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 最终获取到 4 个待审核提交者
2025-07-30 09:18:45.413 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 获取知识点列表
2025-07-30 09:18:45.414 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 使用MyBatis-Plus原生方法获取知识点统计
2025-07-30 09:18:45.435 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 找到 104 个不同的知识点
2025-07-30 09:18:45.530 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:18:45.530 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:18:45.531 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:18:45.531 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:18:45.534 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377760
2025-07-30 09:18:45.536 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377760的重试计数
2025-07-30 09:18:45.557 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:18:45.627 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 统计完成，共 104 个知识点有使用记录
2025-07-30 09:18:45.762 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 最终获取到 104 个知识点
2025-07-30 09:18:46.188 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 分组查询结果 - 总数: 4, 当前页记录数: 4
2025-07-30 09:18:46.380 [http-nio-8081-exec-6] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /admin/correction-approval/statistics
2025-07-30 09:19:17.107 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377760
2025-07-30 09:19:17.114 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:19:34.856 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-07-30 09:19:34.856 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5600a278]]
2025-07-30 09:19:34.856 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-07-30 09:19:35.764 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-30 09:19:35.765 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:19:35.769 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 09:19:35.765 [correction-exec(core=1,max=1)-1] ERROR com.edu.maizi_edu_sys.service.TopicCorrectionService - 处理批次时发生错误
java.lang.RuntimeException: AI服务调用异常: null
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:77) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:142) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_221]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915) ~[?:1.8.0_221]
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:72) ~[classes/:?]
	... 17 more
2025-07-30 09:19:35.781 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-30 09:19:35.783 [correction-exec(core=1,max=1)-1] WARN  com.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@13128a6 marked as broken because of SQLSTATE(08003), ErrorCode(0)
java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1649) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1565) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor139.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy182.prepare(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:87) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy181.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy125.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy151.update(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:265) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1580) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	... 54 more
2025-07-30 09:25:24.892 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:25:24.974 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 17852 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:25:24.979 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:25:26.394 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:25:26.397 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:25:26.464 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:25:26.465 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 49 ms. Found 0 Redis repository interfaces.
2025-07-30 09:25:27.147 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:25:27.155 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:25:27.156 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:25:27.156 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:25:27.446 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:25:27.446 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2418 ms
2025-07-30 09:25:27.623 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:25:27.625 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:25:27.626 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:25:27.628 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:25:27.655 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:25:27.771 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:25:28.152 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.153 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.158 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:25:28.176 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:25:28.178 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:25:28.190 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.190 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.191 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.195 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:25:28.318 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.319 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.319 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.319 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.320 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.320 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.320 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:28.321 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:30.273 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:25:30.277 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:25:31.849 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:25:31.960 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:25:32.074 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:25:32.176 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:25:32.177 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:25:32.244 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 3
2025-07-30 09:25:32.287 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:25:32.494 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 09:25:32.867 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:25:32.897 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-30 09:25:33.191 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:25:33.221 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:25:33.688 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:25:33.756 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:25:33.780 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-30 09:25:33.783 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-30 09:25:33.783 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5600a278]]
2025-07-30 09:25:33.783 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-30 09:25:33.796 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 9.486 seconds (JVM running for 11.861)
2025-07-30 09:25:33.802 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 1 分钟，批大小 3
2025-07-30 09:25:33.821 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377760
2025-07-30 09:25:33.828 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:25:35.055 [RMI TCP Connection(3)-***********] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:25:35.055 [RMI TCP Connection(3)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:25:35.059 [RMI TCP Connection(3)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-30 09:25:48.018 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-07-30 09:25:54.794 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:25:54.794 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:25:54.794 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:25:54.794 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:25:54.796 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377763
2025-07-30 09:25:54.798 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377763的重试计数
2025-07-30 09:25:54.840 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:26:33.816 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377763
2025-07-30 09:26:33.820 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:26:59.835 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:26:59.835 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:26:59.835 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:26:59.836 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:26:59.837 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377766
2025-07-30 09:26:59.838 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377766的重试计数
2025-07-30 09:26:59.850 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:27:33.816 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377766
2025-07-30 09:27:33.818 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:27:48.203 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-07-30 09:27:48.203 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5600a278]]
2025-07-30 09:27:48.203 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-07-30 09:27:49.356 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-30 09:27:49.358 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:27:49.362 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 09:27:49.357 [correction-exec(core=1,max=1)-1] ERROR com.edu.maizi_edu_sys.service.TopicCorrectionService - 处理批次时发生错误
java.lang.RuntimeException: AI服务调用异常: null
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:77) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:142) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_221]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915) ~[?:1.8.0_221]
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:72) ~[classes/:?]
	... 17 more
2025-07-30 09:27:49.376 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-30 09:27:49.379 [correction-exec(core=1,max=1)-1] WARN  com.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@4c140b22 marked as broken because of SQLSTATE(08003), ErrorCode(0)
java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1649) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1565) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor134.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy182.prepare(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:87) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor140.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy181.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor139.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy125.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy151.update(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:265) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1580) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	... 52 more
2025-07-30 09:27:54.409 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:27:54.499 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 12080 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:27:54.504 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:27:55.645 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:27:55.647 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:27:55.700 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:27:55.701 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 42 ms. Found 0 Redis repository interfaces.
2025-07-30 09:27:56.287 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:27:56.294 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:27:56.294 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:27:56.295 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:27:56.539 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:27:56.539 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1990 ms
2025-07-30 09:27:56.752 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:27:56.754 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:27:56.754 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:27:56.758 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:27:56.789 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:27:56.883 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:27:57.404 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.404 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.408 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:27:57.429 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:27:57.431 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:27:57.445 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.446 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.446 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.451 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:27:57.545 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.545 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.545 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.546 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.546 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.546 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.547 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:57.547 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:27:59.503 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:27:59.507 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:28:00.458 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:28:00.564 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:28:00.643 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:28:00.749 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:28:00.749 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:28:00.815 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 3
2025-07-30 09:28:00.847 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:28:01.022 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 09:28:01.360 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:28:01.390 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-30 09:28:01.649 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:28:01.682 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:28:02.194 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:28:02.269 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:28:02.290 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-30 09:28:02.292 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-30 09:28:02.293 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5600a278]]
2025-07-30 09:28:02.293 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-30 09:28:02.307 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 8.335 seconds (JVM running for 10.316)
2025-07-30 09:28:02.314 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 1 分钟，批大小 3
2025-07-30 09:28:02.337 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377766
2025-07-30 09:28:02.344 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:28:03.663 [RMI TCP Connection(2)-***********] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:28:03.664 [RMI TCP Connection(2)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:28:03.666 [RMI TCP Connection(2)-***********] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-30 09:28:13.694 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Login attempt from IP: 0:0:0:0:0:0:0:1, username: sxq
2025-07-30 09:28:13.811 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Generating token for user: sxq with secret (first 5): 'F9A8C...'
2025-07-30 09:28:14.856 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-07-30 09:28:15.301 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/info called (redirecting to /current)
2025-07-30 09:28:15.301 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-07-30 09:28:15.303 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'sxq' extracted from valid token
2025-07-30 09:28:15.310 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: sxq
2025-07-30 09:28:19.511 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-07-30 09:28:19.515 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'sxq' extracted from valid token
2025-07-30 09:28:19.521 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: sxq
2025-07-30 09:28:19.549 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:28:19.554 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户统计数据, userId: 1920280447393230872
2025-07-30 09:28:19.574 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户 1920280447393230872 统计数据: 总数=20, 通过=0, 待审核=20, 拒绝=0
2025-07-30 09:28:19.595 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的上传趋势数据，类型: day, 天数: 7
2025-07-30 09:28:19.600 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户上传趋势, userId: 1920280447393230872, days: 7
2025-07-30 09:28:19.609 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户排名计算已简化，避免内存溢出。用户1920280447393230872上传量: 20
2025-07-30 09:28:19.617 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:28:19.641 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传20, 已通过0, 待审核20
2025-07-30 09:28:27.278 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-07-30 09:28:27.281 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'sxq' extracted from valid token
2025-07-30 09:28:27.285 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: sxq
2025-07-30 09:28:27.301 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:28:27.302 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:28:27.321 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传20, 已通过0, 待审核20
2025-07-30 09:28:27.353 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的上传趋势数据，类型: day, 天数: 7
2025-07-30 09:28:27.353 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户上传趋势, userId: 1920280447393230872, days: 7
2025-07-30 09:28:28.983 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:28:28.984 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:28:28.984 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:28:28.984 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:28:28.986 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377769
2025-07-30 09:28:28.987 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377769的重试计数
2025-07-30 09:28:29.004 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:29:02.320 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377769
2025-07-30 09:29:02.322 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:29:25.560 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:29:25.560 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:29:25.560 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:29:25.560 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:29:25.562 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377772
2025-07-30 09:29:25.564 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377772的重试计数
2025-07-30 09:29:25.574 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:29:27.369 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:29:27.369 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:29:27.380 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传20, 已通过0, 待审核20
2025-07-30 09:29:35.925 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.TopicController - Received topic upload request with 30 topics
2025-07-30 09:29:35.941 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 批量提交题目审核, userId: 1920280447393230872, count: 30
2025-07-30 09:29:35.999 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 成功批量插入30条审核记录
2025-07-30 09:29:35.999 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 批量题目提交审核完成，共30条
2025-07-30 09:29:36.004 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.TopicController - User (role=1, userId=1920280447393230872) submitted 30 topics for audit
2025-07-30 09:29:36.006 [http-nio-8081-exec-4] INFO  org.springframework.scheduling.annotation.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [correctionExecutor, clientInboundChannelExecutor, clientOutboundChannelExecutor, brokerChannelExecutor]
2025-07-30 09:29:38.721 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-07-30 09:29:38.723 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'sxq' extracted from valid token
2025-07-30 09:29:38.727 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: sxq
2025-07-30 09:29:38.744 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:29:38.745 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户统计数据, userId: 1920280447393230872
2025-07-30 09:29:38.755 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户 1920280447393230872 统计数据: 总数=50, 通过=0, 待审核=50, 拒绝=0
2025-07-30 09:29:38.775 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户排名计算已简化，避免内存溢出。用户1920280447393230872上传量: 50
2025-07-30 09:29:38.776 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:29:38.788 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传50, 已通过0, 待审核50
2025-07-30 09:29:38.793 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的上传趋势数据，类型: day, 天数: 7
2025-07-30 09:29:38.794 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户上传趋势, userId: 1920280447393230872, days: 7
2025-07-30 09:30:02.330 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377772
2025-07-30 09:30:02.332 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:30:23.609 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:30:23.609 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:30:23.609 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:30:23.609 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:30:23.610 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377775
2025-07-30 09:30:23.611 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377775的重试计数
2025-07-30 09:30:23.823 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:30:25.179 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.TopicController - Received topic upload request with 30 topics
2025-07-30 09:30:25.188 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 批量提交题目审核, userId: 1920280447393230872, count: 30
2025-07-30 09:30:25.227 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 成功批量插入30条审核记录
2025-07-30 09:30:25.227 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 批量题目提交审核完成，共30条
2025-07-30 09:30:25.232 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.TopicController - User (role=1, userId=1920280447393230872) submitted 30 topics for audit
2025-07-30 09:30:39.046 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:30:39.046 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户统计数据, userId: 1920280447393230872
2025-07-30 09:30:39.054 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户 1920280447393230872 统计数据: 总数=80, 通过=0, 待审核=80, 拒绝=0
2025-07-30 09:30:39.069 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户排名计算已简化，避免内存溢出。用户1920280447393230872上传量: 80
2025-07-30 09:30:39.069 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:30:39.077 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传80, 已通过0, 待审核80
2025-07-30 09:31:01.384 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Generating token for user: sxq with secret (first 5): 'F9A8C...'
2025-07-30 09:31:02.336 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377775
2025-07-30 09:31:02.337 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:31:08.842 [http-nio-8081-exec-7] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /admin/correction-approval/statistics
2025-07-30 09:31:26.552 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:31:26.552 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:31:26.552 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:31:26.552 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:31:26.553 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377778
2025-07-30 09:31:26.554 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377778的重试计数
2025-07-30 09:31:26.562 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:31:39.046 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:31:39.046 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:31:39.057 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传80, 已通过0, 待审核80
2025-07-30 09:31:53.133 [http-nio-8081-exec-7] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /admin/correction-approval/statistics
2025-07-30 09:32:02.344 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377778
2025-07-30 09:32:02.347 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:32:25.008 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-07-30 09:32:25.008 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5600a278]]
2025-07-30 09:32:25.008 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-07-30 09:32:25.911 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-30 09:32:25.912 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:32:25.914 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 09:32:25.912 [correction-exec(core=1,max=1)-1] ERROR com.edu.maizi_edu_sys.service.TopicCorrectionService - 处理批次时发生错误
java.lang.RuntimeException: AI服务调用异常: null
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:77) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:142) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_221]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915) ~[?:1.8.0_221]
	at com.edu.maizi_edu_sys.util.DoubaoSyncUtil.syncRequest(DoubaoSyncUtil.java:72) ~[classes/:?]
	... 17 more
2025-07-30 09:32:25.925 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-30 09:32:25.927 [correction-exec(core=1,max=1)-1] WARN  com.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Connection com.mysql.cj.jdbc.ConnectionImpl@4965fb52 marked as broken because of SQLSTATE(08003), ErrorCode(0)
java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1649) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1565) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor136.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy182.prepare(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:87) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor200.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy181.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor225.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy125.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy151.update(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService.checkAndProcessOneBatch(TopicCorrectionService.java:265) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$FastClassBySpringCGLIB$$c6cc414b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.TopicCorrectionService$$EnhancerBySpringCGLIB$$63011bcb.checkAndProcessOneBatch(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.runner.TopicCorrectionRunner.lambda$schedule$0(TopicCorrectionRunner.java:70) ~[classes/:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_221]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_221]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_221]
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.prepareStatement(ConnectionImpl.java:1580) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	... 52 more
2025-07-30 09:49:34.006 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:49:34.069 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 21396 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:49:34.111 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:49:35.326 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:49:35.328 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:49:35.383 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:49:35.384 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 44 ms. Found 0 Redis repository interfaces.
2025-07-30 09:49:35.977 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:49:35.984 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:49:35.984 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:49:35.984 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:49:36.218 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:49:36.218 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2050 ms
2025-07-30 09:49:36.373 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:49:36.374 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:49:36.375 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:49:36.377 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:49:36.406 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:49:36.528 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:49:36.843 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.843 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.847 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:49:36.863 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:49:36.865 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:49:36.878 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.879 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.879 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.883 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:49:36.982 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.983 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.983 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.983 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.984 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.984 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.984 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:36.984 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:38.902 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:49:38.905 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:49:39.957 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:49:40.089 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:49:40.167 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:49:40.281 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:49:40.282 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:49:40.344 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 3
2025-07-30 09:49:40.378 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:49:40.533 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 09:49:40.861 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:49:40.901 [main] INFO  com.edu.maizi_edu_sys.config.UserStatsCacheConfig - 用户统计缓存管理器初始化完成: maxSize=1000, expireMinutes=30
2025-07-30 09:49:41.202 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:49:41.229 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:49:41.822 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:49:41.908 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:49:41.925 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-30 09:49:41.927 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-30 09:49:41.927 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2e44cb34]]
2025-07-30 09:49:41.927 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-30 09:49:41.942 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 8.522 seconds (JVM running for 10.687)
2025-07-30 09:49:41.948 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 1 分钟，批大小 3
2025-07-30 09:49:41.966 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377811
2025-07-30 09:49:41.971 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:49:44.115 [http-nio-8081-exec-10] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:49:44.115 [http-nio-8081-exec-10] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:49:44.117 [http-nio-8081-exec-10] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-30 09:49:44.141 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-30 09:49:44.141 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-30 09:49:44.141 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-30 09:49:44.141 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-30 09:49:44.141 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-30 09:49:44.141 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-30 09:49:47.349 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:49:47.353 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户统计数据, userId: 1920280447393230872
2025-07-30 09:49:47.367 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户 1920280447393230872 统计数据: 总数=80, 通过=0, 待审核=80, 拒绝=0
2025-07-30 09:49:47.401 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 用户排名计算已简化，避免内存溢出。用户1920280447393230872上传量: 80
2025-07-30 09:49:47.405 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:49:47.418 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传80, 已通过0, 待审核80
2025-07-30 09:49:49.047 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-30 09:49:49.048 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-30 09:49:49.048 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-30 09:49:49.048 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-30 09:49:49.048 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-30 09:49:49.048 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-30 09:50:03.723 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:50:03.723 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:50:03.723 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:50:03.723 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:50:03.725 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377814
2025-07-30 09:50:03.727 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377814的重试计数
2025-07-30 09:50:03.743 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:50:07.493 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-07-30 09:50:07.497 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'sxq' extracted from valid token
2025-07-30 09:50:07.504 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: sxq
2025-07-30 09:50:07.509 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:50:07.510 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:50:07.523 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传80, 已通过0, 待审核80
2025-07-30 09:50:07.552 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的上传趋势数据，类型: day, 天数: 7
2025-07-30 09:50:07.552 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户上传趋势, userId: 1920280447393230872, days: 7
2025-07-30 09:50:13.100 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-30 09:50:13.100 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-30 09:50:13.100 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-30 09:50:13.102 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-30 09:50:13.102 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-30 09:50:13.102 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-30 09:50:14.122 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/fitness-radar/all
2025-07-30 09:50:14.122 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/papers
2025-07-30 09:50:14.122 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/overview
2025-07-30 09:50:14.122 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/convergence/all
2025-07-30 09:50:14.123 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/population/all
2025-07-30 09:50:14.123 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor/api/constraints/all
2025-07-30 09:50:14.682 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Admin page request without token: /admin/algorithm-monitor
2025-07-30 09:50:21.414 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Generating token for user: sxq with secret (first 5): 'F9A8C...'
2025-07-30 09:50:39.630 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 获取用户每日审核分组数据 - 页码: 1, 大小: 10, 关键词: 
2025-07-30 09:50:39.669 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 审核表总记录数: 83500
2025-07-30 09:50:39.671 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 审核统计数据: pending=521, approved=82856, rejected=123, total=83500
2025-07-30 09:50:40.421 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - === 开始获取提交者列表 ===
2025-07-30 09:50:40.441 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - topic_audit表总记录数: 83500
2025-07-30 09:50:40.486 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 有user_id的记录数: 83500
2025-07-30 09:50:40.488 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 待审核记录数: 521
2025-07-30 09:50:40.490 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 待审核且有user_id的记录数: 521
2025-07-30 09:50:40.490 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 开始查询待审核提交者信息
2025-07-30 09:50:40.491 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 执行统计查询SQL: (audit_status = #{ew.paramNameValuePairs.MPGENVAL1} AND user_id IS NOT NULL) GROUP BY user_id ORDER BY COUNT(*) DESC
2025-07-30 09:50:40.495 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 获取到 4 个待审核提交者的统计信息
2025-07-30 09:50:40.495 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 统计信息示例: [{user_id=1920280447393230859, count=209}, {user_id=1920280447393230860, count=142}, {user_id=1920280447393230855, count=90}]
2025-07-30 09:50:40.496 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 需要查询的用户ID列表: [1920280447393230859, 1920280447393230860, 1920280447393230855, 1920280447393230872]
2025-07-30 09:50:40.500 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 获取到 4 个用户的详细信息
2025-07-30 09:50:40.500 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 用户信息示例: [{id=1920280447393230859, username=王女杰}, {id=1920280447393230860, username=吴宇恒}, {id=1920280447393230855, username=adminaa}]
2025-07-30 09:50:40.501 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 合并后获取到 4 个有效的提交者信息
2025-07-30 09:50:40.501 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 最终获取到 4 个待审核提交者
2025-07-30 09:50:40.518 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 获取知识点列表
2025-07-30 09:50:40.518 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 使用MyBatis-Plus原生方法获取知识点统计
2025-07-30 09:50:40.540 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 找到 104 个不同的知识点
2025-07-30 09:50:40.773 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 统计完成，共 104 个知识点有使用记录
2025-07-30 09:50:40.867 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 分组查询结果 - 总数: 5, 当前页记录数: 5
2025-07-30 09:50:40.925 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl - 最终获取到 104 个知识点
2025-07-30 09:50:41.953 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377814
2025-07-30 09:50:41.954 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:51:08.052 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:51:08.052 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:51:08.059 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传80, 已通过0, 待审核80
2025-07-30 09:51:22.328 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:51:22.328 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:51:22.328 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:51:22.328 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:51:22.329 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377817
2025-07-30 09:51:22.330 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377817的重试计数
2025-07-30 09:51:22.341 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:51:41.965 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377817
2025-07-30 09:51:41.967 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:52:08.057 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:52:08.057 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:52:08.063 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传80, 已通过0, 待审核80
2025-07-30 09:52:19.504 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:52:19.505 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:52:19.505 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:52:19.505 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:52:19.506 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377820
2025-07-30 09:52:19.507 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377820的重试计数
2025-07-30 09:52:19.517 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:52:41.978 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377820
2025-07-30 09:52:41.979 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:53:08.055 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:53:08.056 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:53:08.062 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传80, 已通过0, 待审核80
2025-07-30 09:53:17.601 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:53:17.601 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:53:17.601 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:53:17.601 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:53:17.602 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377823
2025-07-30 09:53:17.603 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377823的重试计数
2025-07-30 09:53:17.650 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:53:30.173 [http-nio-8081-exec-6] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /admin/correction-approval/statistics
2025-07-30 09:53:41.992 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377823
2025-07-30 09:53:41.993 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:54:08.061 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:54:08.061 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:54:08.068 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传80, 已通过0, 待审核80
2025-07-30 09:54:16.214 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:54:16.214 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:54:16.214 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:54:16.214 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:54:16.216 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377826
2025-07-30 09:54:16.216 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377826的重试计数
2025-07-30 09:54:16.229 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:54:41.995 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377826
2025-07-30 09:54:41.996 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:55:04.231 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:55:04.231 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:55:04.231 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:55:04.231 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:55:04.233 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377829
2025-07-30 09:55:04.234 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377829的重试计数
2025-07-30 09:55:04.245 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:55:08.052 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:55:08.052 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:55:08.057 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传80, 已通过0, 待审核80
2025-07-30 09:55:42.002 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377829
2025-07-30 09:55:42.003 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:56:04.183 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:56:04.183 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:56:04.184 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:56:04.184 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:56:04.185 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377832
2025-07-30 09:56:04.185 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377832的重试计数
2025-07-30 09:56:04.196 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:56:09.058 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:56:09.058 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:56:09.064 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传80, 已通过0, 待审核80
2025-07-30 09:56:42.004 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377832
2025-07-30 09:56:42.005 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:57:01.330 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:57:01.330 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:57:01.330 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:57:01.330 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:57:01.331 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377835
2025-07-30 09:57:01.332 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377835的重试计数
2025-07-30 09:57:01.344 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:57:10.051 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:57:10.052 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:57:10.057 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传80, 已通过0, 待审核80
2025-07-30 09:57:42.011 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377835
2025-07-30 09:57:42.012 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:58:06.885 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-30 09:58:06.885 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-30 09:58:06.885 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-30 09:58:06.885 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-30 09:58:06.886 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377838
2025-07-30 09:58:06.887 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377838的重试计数
2025-07-30 09:58:06.897 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:58:11.058 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:58:11.058 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:58:11.063 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传80, 已通过0, 待审核80
2025-07-30 09:58:41.357 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:58:41.489 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 18584 (D:\IdeaProject\maizi_edu_sys\target\app.jar started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-30 09:58:41.495 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 09:58:42.025 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377838
2025-07-30 09:58:42.027 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 3 个题目进行处理
2025-07-30 09:58:44.061 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:58:44.064 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:58:44.139 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 09:58:44.140 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 57 ms. Found 0 Redis repository interfaces.
2025-07-30 09:58:46.579 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-30 09:58:46.591 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:58:46.592 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:58:46.592 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 09:58:46.790 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:58:46.791 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5219 ms
2025-07-30 09:58:47.079 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-30 09:58:47.086 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-30 09:58:47.087 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-30 09:58:47.094 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-30 09:58:47.150 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-30 09:58:47.341 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-30 09:58:47.385 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-30 09:58:47.973 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:47.974 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:47.981 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:58:48.011 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 09:58:48.015 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 09:58:48.034 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.035 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.037 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.043 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 09:58:48.201 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.202 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.203 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.204 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.205 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.205 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.206 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:48.207 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:50.073 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-30 09:58:51.015 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-30 09:58:52.349 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-30 09:58:52.523 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API�ͻ��˳�ʼ����ɣ�baseUrl: https://mineru.net, timeout: 60s
2025-07-30 09:58:52.708 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 09:58:52.710 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-30 09:58:52.829 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - ��Ŀ���������ʼ����ɣ��������С: 5
2025-07-30 09:58:52.895 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:58:56.375 [main] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:829) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.selectOne(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.getConfigValue(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.getConfigValue(SystemConfigServiceImpl.java:178) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.initDefaultConfigs(SystemConfigServiceImpl.java:273) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.init(SystemConfigServiceImpl.java:172) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:24) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[app.jar:?]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88) ~[app.jar:?]
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	... 103 more
Caused by: java.net.UnknownHostException: db
	at java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method) ~[?:1.8.0_221]
	at java.net.InetAddress$2.lookupAllHostAddr(InetAddress.java:929) ~[?:1.8.0_221]
	at java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1324) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName0(InetAddress.java:1277) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1193) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1127) ~[?:1.8.0_221]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:133) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	... 103 more
2025-07-30 09:58:56.389 [main] ERROR com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - ��ȡ����ֵʧ��: topic_correction_prompt
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/edu/maizi_edu_sys/mapper/SystemConfigMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.SystemConfigMapper.getConfigValue
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.selectOne(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.getConfigValue(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.getConfigValue(SystemConfigServiceImpl.java:178) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.initDefaultConfigs(SystemConfigServiceImpl.java:273) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.init(SystemConfigServiceImpl.java:172) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:24) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[app.jar:?]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88) ~[app.jar:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/edu/maizi_edu_sys/mapper/SystemConfigMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.SystemConfigMapper.getConfigValue
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:829) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: java.net.UnknownHostException: db
	at java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method) ~[?:1.8.0_221]
	at java.net.InetAddress$2.lookupAllHostAddr(InetAddress.java:929) ~[?:1.8.0_221]
	at java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1324) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName0(InetAddress.java:1277) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1193) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1127) ~[?:1.8.0_221]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:133) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
2025-07-30 09:58:56.404 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 09:58:57.411 [main] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:829) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.initDefaultConfigs(SystemConfigServiceImpl.java:274) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.init(SystemConfigServiceImpl.java:172) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:24) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[app.jar:?]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88) ~[app.jar:?]
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	... 104 more
Caused by: java.net.UnknownHostException: db
	at java.net.InetAddress.getAllByName0(InetAddress.java:1281) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1193) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1127) ~[?:1.8.0_221]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:133) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	... 104 more
2025-07-30 09:58:57.419 [main] ERROR com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - ��������ʧ��: topic_correction_prompt = ����һλ�����Ľ���������������ר�ң������ǳ䵱�Զ���У��ϵͳ��������ݿ��еĽ�����Ŀ��

��ĺ���Ŀ�����ҳ���Ŀ�е��κδ��󣬲������ϸ��JSON��ʽ������Ҫ���������ݣ��Ա�������Զ��������ݿ⡣

## ���������������ʽ�淶

�㽫�յ�һ�����������Ŀ��JSON���顣����ϸ���ÿһ���⡣

### 1. ������ִ���

�� **����** ����һ��JSON���飬���а���һ����������ÿ���������һ����Ҫ��������Ŀ��
ÿ������ **����** ������������

- **"id"**: ��Ŀ������ID�����ڶ�λ���ݿ��¼
- **"reason"**: һ���ַ�������һ�仰�����ظ����������޸ĵĺ���ԭ�����磺"ԭ�𰸴���"��"������׼ȷ"��"����д����"��"��Ŀ��������"
- **"updates"**: һ���������� **ֻ����** ��Ҫ���޸ĵ��ֶκ����ǵ���ֵ�����Բ�Ҫ����û�иĶ����ֶ�

**��Ҫ**��updates �����пɰ������ֶ�Ϊ: `type`, `title`, `options`, `subs`, `answer`, `parse`, `difficulty`

?? **�ر�ע�� `options` / `subs` �ĸ�ʽ**��
- �������ֶ����ջ��� JSON �ַ�����ʽ�������ݿ⣬�� **���÷��� updates ��Ӧֱ���ṩ�Ϸ��� JSON ����/����**��ϵͳ���Զ����л���
- ��ȷʾ����`"options": [{"key":"A","name":"ѡ��A"},{"key":"B","name":"ѡ��B"}]`

**�� updates ����Ϊ�գ���û���ֶ���Ҫ�޸ģ����벻Ҫ���ظ��⡣**

### ���ʾ����

```json
[
  {
    "id": 275735,
    "reason": "��ѡ�����ĸ˳������ҽ���������ϸ",
    "updates": {
      "answer": "ACD",
      "options": [{"key":"A","name":"��ȷѡ��A"},{"key":"B","name":"����ѡ��B"},{"key":"C","name":"��ȷѡ��C"},{"key":"D","name":"��ȷѡ��D"}],
      "parse": "��������Ľ�����A����ȷ����Ϣ���м�ֵ����ԣ����ֵ���ˡ���ʱ����ض��졣B�������Ϣ��Ȼ���Թ������ڴ��������п��ܻ���Ļ�ʧ�档C����ȷ����Ϣ����ʱЧ�ԣ�������ʱ�����ƶ�ʧȥ�򽵵ͼ�ֵ��D����ȷ����Ϣ�������������ԣ�����ͨ��ĳ����ʽ�����������ء�"
    }
  }
]
```

**ʧ��ʾ��������д��������ģ�£�**��

```json
{
  "id": 1,
  "reason": "ʾ��"
}
```

### 2. ���û�з����κδ���

��������������Ŀ��������ȱ���� **����** ����һ���յ�JSON���� `[]`��

## ��������嵥

### �������߼�������

#### ��� (title) ��飺
- �Ƿ��д���֡��ﲡ�������Ŵ���
- �����Ƿ�������ȷ����������
- ���ʷ�ʽ�Ƿ�ǡ����"�����ĸ�"��"����...����˵����ȷ����"�ȣ�
- �Ƿ��������Ҫ���ظ��ʻ�

#### ѡ�� (options) ��飺
- ÿ��ѡ���Ƿ��д���ֻ��﷨����
- ѡ����Ƿ����У������Ƿ���
- ��ѡ�⣺�Ƿ�ֻ��һ����ȷ��ȷ��
- ��ѡ�⣺�Ƿ���2-4����ȷѡ���ѡ���û���ظ���ì��
- ѡ���Ƿ���к�����Ի��ԣ�����ѡ��Ӧ�ÿ��ƺ���
- options��ʽ����������Ч��JSON�����ʽ
- **ѡ����𰸱���ƥ��**������ĸ����ȫ�������� options.key �У�

#### �� (answer) ��飺
- ���Ƿ���ȷ��Ӧ���������
- ������������Ƿ���ȫһ��
- ��ʽ�淶��
  - ��ѡ�� (choice)�������ǵ�����д��ĸ���� "A"��"B"��"C"��"D"������ "E" **���ȱ���Ϊ 1**
  - ��ѡ�� (multiple)�������Ǵ�д��ĸ�� **����ĸ��������**���� "ACD"�������� "CAD"����**��ȷѡ������ 2-5 ��**
  - �ж��� (judge)�������� "��" �� "��"������ʹ������ȫ���ַ���

#### ���� (parse) ��飺
- �Ƿ�������������ȷ��Ϊʲô��ȷ
- �Ƿ�����˴���ѡ��Ϊʲô����
- ���õ�֪ʶ�㡢��ʵ�������Ƿ�׼ȷ
- �����Ƿ�רҵ���꾡���ܰ���ѧ�����
- ��������ظ��𰸣�Ҫ�н�����ֵ

### ��ʽ��淶���

#### ��Ŀ���� (type) ��飺
- **choice**����ѡ�⣬��Ϊ������ĸ
- **multiple**����ѡ�⣬��Ϊ�����ĸ
- **judge**���ж��⣬��Ϊ"��"��"��"
- type�Ƿ���ʵ����Ŀ��ʽƥ��

#### �Ѷ� (difficulty) ������
- ȡֵ��Χ��0.1 - 1.0��**����һλС��**��
- �ο���׼��
  - 0.1-0.3�����������⣬ֱ�Ӽ�����
  - 0.4-0.6�����Ӧ���⣬��Ҫ˼������
  - 0.7-0.9���ۺϷ����⣬��Ҫ������
  - 1.0�������⣬��Ҫ����˼ά

### ������������ʾ��

1. **�𰸸�ʽ����**��
   - ��ѡ��� "CAD" �� "ACD"
   - ��ѡ��� "ab" �� "A"

2. **���Ͳ�ƥ��**��
   - ���� "AC" �� type �� "choice" �� type ӦΪ "multiple"

3. **������������**��
   - "A��ȷ��B����C����D����" �� ��ϸ����ÿ��ѡ���ԭ��

4. **�߼���һ��**��
   - ����˵A��ȷ��������B �� �𰸺ͽ�������һ��

### ��Ҫ����

**���Բ����޸ĵ��ֶ�**��`id`, `know_id`, `tags`, `source`, `score`, `sort`, `created_at`
���������������Ŀ���ݱ���������Ԫ���ݷ��ࡣ

### ������׼

ÿ����Ŀ������Ӧ�ﵽ��
- ����׼ȷ���󣬷���ѧ��֪ʶ
- ��ʽ�淶ͳһ������ϵͳ����
- ������ϸרҵ�����н�����ֵ
- �Ѷ����ú���ƥ����Ŀ���Ӷ�

---
?? **��������Ǵ� JSON**���ش���ֻ�ܰ����Ϸ��� JSON ���飬**ǰ�󲻿ɼд��κν��������֡�Markdown��������ǻ������ַ�**��

���ڣ�������������й�����ϸ�������������Ŀ��
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/edu/maizi_edu_sys/mapper/SystemConfigMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.SystemConfigMapper.insertOrUpdateConfig
### The error occurred while executing an update
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.initDefaultConfigs(SystemConfigServiceImpl.java:274) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.init(SystemConfigServiceImpl.java:172) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:24) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[app.jar:?]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88) ~[app.jar:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/edu/maizi_edu_sys/mapper/SystemConfigMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.SystemConfigMapper.insertOrUpdateConfig
### The error occurred while executing an update
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:196) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:829) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
Caused by: java.net.UnknownHostException: db
	at java.net.InetAddress.getAllByName0(InetAddress.java:1281) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1193) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1127) ~[?:1.8.0_221]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:133) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	... 70 more
2025-07-30 09:58:57.441 [main] ERROR com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - ��ʼ��Ĭ������ʧ��
java.lang.RuntimeException: ��������ʧ��
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:199) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.initDefaultConfigs(SystemConfigServiceImpl.java:274) ~[classes!/:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.init(SystemConfigServiceImpl.java:172) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar!/:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar!/:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:24) ~[classes!/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108) ~[app.jar:?]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58) ~[app.jar:?]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88) ~[app.jar:?]
Caused by: org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/edu/maizi_edu_sys/mapper/SystemConfigMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.SystemConfigMapper.insertOrUpdateConfig
### The error occurred while executing an update
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	... 63 more
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/edu/maizi_edu_sys/mapper/SystemConfigMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.SystemConfigMapper.insertOrUpdateConfig
### The error occurred while executing an update
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:196) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	... 63 more
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	... 63 more
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:829) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	... 63 more
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_221]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_221]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	... 63 more
Caused by: java.net.UnknownHostException: db
	at java.net.InetAddress.getAllByName0(InetAddress.java:1281) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1193) ~[?:1.8.0_221]
	at java.net.InetAddress.getAllByName(InetAddress.java:1127) ~[?:1.8.0_221]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:133) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.28.jar!/:8.0.28]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar!/:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar!/:?]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.23.jar!/:5.3.23]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar!/:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar!/:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar!/:3.5.7]
	at com.sun.proxy.$Proxy177.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar!/:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_221]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_221]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_221]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_221]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.sun.proxy.$Proxy123.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.0.6.jar!/:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar!/:*******]
	at com.sun.proxy.$Proxy175.insertOrUpdateConfig(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl.setConfigValue(SystemConfigServiceImpl.java:195) ~[classes!/:?]
	... 63 more
2025-07-30 09:58:57.709 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-30 09:58:58.570 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-30 09:58:58.620 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-30 09:58:59.586 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:58:59.657 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-30 09:58:59.669 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-07-30 09:58:59.686 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner �����رգ��ȴ�����ִ�е��������...
2025-07-30 09:58:59.688 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:58:59.703 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8081"]
2025-07-30 09:58:59.704 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-30 09:58:59.709 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8081"]
2025-07-30 09:58:59.709 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8081"]
2025-07-30 09:58:59.726 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 09:58:59.755 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-30 09:59:12.052 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 获取用户1920280447393230872的个人上传统计数据
2025-07-30 09:59:12.053 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.UserStatsServiceImpl - 获取用户题目类型分布, userId: 1920280447393230872
2025-07-30 09:59:12.058 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserStatsController - 成功获取用户1920280447393230872的统计数据: 总上传80, 已通过0, 待审核80
2025-07-30 09:59:13.550 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 514
2025-07-30 09:59:13.554 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 514
2025-07-30 09:59:13.555 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 1 项修正建议，其中 1 项有效
2025-07-30 09:59:13.566 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2349, 日期: 2025-07-30, 过期时间: 2025-09-28 09:59:13
2025-07-30 09:59:13.566 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 1 条修正建议待审核，审核ID: 2349
2025-07-30 09:59:13.567 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377841
2025-07-30 09:59:13.568 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377841的重试计数
2025-07-30 09:59:13.584 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 3 个题目，服务将在 1 分钟后处理下一批。
2025-07-30 09:59:16.148 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-07-30 09:59:16.149 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2e44cb34]]
2025-07-30 09:59:16.149 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-07-30 09:59:17.324 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-30 09:59:17.326 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-30 09:59:17.331 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 09:59:17.340 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
