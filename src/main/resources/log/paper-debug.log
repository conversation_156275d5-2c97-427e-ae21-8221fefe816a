2025-07-30 09:00:26.051 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-30 09:00:26.169 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-30 09:00:26.169 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-30 09:00:26.194 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-30 09:06:59.716 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-30 09:06:59.822 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-30 09:06:59.823 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-30 09:06:59.843 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-30 09:07:48.717 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-30 09:07:48.820 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-30 09:07:48.820 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-30 09:07:48.839 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-30 09:13:55.991 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-30 09:13:56.087 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-30 09:13:56.087 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-30 09:13:56.107 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-30 09:14:12.455 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-30 09:14:12.565 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-30 09:14:12.565 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-30 09:14:12.588 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-30 09:18:14.861 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-30 09:18:14.999 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-30 09:18:14.999 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-30 09:18:15.034 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-30 09:25:32.069 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-30 09:25:32.159 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-30 09:25:32.159 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-30 09:25:32.182 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-30 09:28:00.637 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-30 09:28:00.734 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-30 09:28:00.734 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-30 09:28:00.754 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-30 09:49:40.161 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-30 09:49:40.266 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-30 09:49:40.266 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-30 09:49:40.286 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-30 09:58:52.650 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-30 09:58:52.677 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-30 09:58:52.677 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-30 09:58:52.723 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-30 10:00:35.318 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-30 10:00:35.410 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-30 10:00:35.410 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-30 10:00:35.430 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
