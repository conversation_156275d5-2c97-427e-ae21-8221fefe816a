/**
 * 用户管理页面JavaScript
 */

// ID处理工具 - 解决JavaScript大数值精度问题
class IdUtils {
    // 确保ID始终作为字符串处理
    static ensureString(id) {
        if (typeof id === 'number') {
            console.warn('检测到数值型ID，可能存在精度问题:', id);
            return id.toString();
        }
        return String(id);
    }
    
    // 检查ID是否超过JavaScript安全范围
    static isSafeId(id) {
        const numId = Number(id);
        return Number.isSafeInteger(numId);
    }
    
    // 验证ID格式
    static validate(id) {
        const strId = this.ensureString(id);
        if (!strId || strId.trim() === '') {
            return { valid: false, error: 'ID不能为空' };
        }
        if (!/^\d+$/.test(strId)) {
            return { valid: false, error: 'ID格式无效' };
        }
        if (!this.isSafeId(strId)) {
            console.warn('ID超过JavaScript安全范围，请确保以字符串形式处理:', strId);
        }
        return { valid: true, id: strId };
    }
    
    // 批量处理ID数组
    static processBatch(ids) {
        return ids.map(id => this.ensureString(id));
    }
}

class UserManagement {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.searchParams = {
            search: '',
            role: '',
            status: ''
        };
        this.selectedUsers = new Set();
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadUserStats();
        this.loadUsers();
    }

    bindEvents() {
        // 搜索框事件
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.searchParams.search = e.target.value;
                    this.currentPage = 1;
                    this.loadUsers();
                }, 500);
            });
        }

        // 筛选器事件
        const roleFilter = document.getElementById('roleFilter');
        if (roleFilter) {
            roleFilter.addEventListener('change', (e) => {
                this.searchParams.role = e.target.value;
                this.currentPage = 1;
                this.loadUsers();
            });
        }

        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.searchParams.status = e.target.value;
                this.currentPage = 1;
                this.loadUsers();
            });
        }

        // 全选复选框
        const selectAll = document.getElementById('selectAll');
        if (selectAll) {
            selectAll.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }

        // 批量删除按钮
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', () => {
                this.batchDeleteUsers();
            });
        }

        // 添加用户表单提交
        const addUserForm = document.getElementById('addUserForm');
        if (addUserForm) {
            addUserForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.addUser();
            });
        }

        // 编辑用户表单提交
        const editUserForm = document.getElementById('editUserForm');
        if (editUserForm) {
            editUserForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.updateUser();
            });
        }
    }

    // 加载用户统计数据
    async loadUserStats() {
        try {
            const response = await fetch('/api/admin/users/stats');
            if (response.ok) {
                const stats = await response.json();
                this.updateStatsDisplay(stats);
            }
        } catch (error) {
            console.error('加载用户统计失败:', error);
        }
    }

    updateStatsDisplay(stats) {
        const totalUsers = document.getElementById('totalUsers');
        const activeUsers = document.getElementById('activeUsers');
        const disabledUsers = document.getElementById('disabledUsers');
        const adminUsers = document.getElementById('adminUsers');

        if (totalUsers) totalUsers.textContent = stats.totalUsers || 0;
        if (activeUsers) activeUsers.textContent = stats.activeUsers || 0;
        if (disabledUsers) disabledUsers.textContent = stats.disabledUsers || 0;
        if (adminUsers) adminUsers.textContent = stats.adminUsers || 0;
    }

    // 加载用户列表
    async loadUsers() {
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                size: this.pageSize,
                ...this.searchParams
            });

            const response = await fetch(`/api/admin/users?${params}`);
            if (response.ok) {
                const data = await response.json();
                this.renderUserTable(data.records || []);
                this.renderPagination(data);
            } else {
                throw new Error('加载用户列表失败');
            }
        } catch (error) {
            console.error('加载用户列表失败:', error);
            this.showError('加载用户列表失败');
        }
    }

    renderUserTable(users) {
        const tbody = document.getElementById('userTableBody');
        if (!tbody) return;

        if (users.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <p class="text-muted">暂无用户数据</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = users.map(user => {
            const userId = IdUtils.ensureString(user.id);
            const isSelected = this.selectedUsers.has(userId);

            return `
                <tr>
                    <td>
                        <input type="checkbox" class="user-checkbox"
                               value="${userId}" ${isSelected ? 'checked' : ''}>
                    </td>
                    <td>${userId}</td>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="${this.getAvatarUrl(user.avatar)}"
                                 alt="头像" class="avatar me-2"
                                 onerror="this.src='/images/default-avatar.png'">
                            <span>${this.escapeHtml(user.username || '')}</span>
                        </div>
                    </td>
                    <td>${this.escapeHtml(user.email || '')}</td>
                    <td>${this.escapeHtml(user.phone || '')}</td>
                    <td>${this.getRoleText(user.role)}</td>
                    <td>${this.getStatusBadge(user.status)}</td>
                    <td>${this.formatDate(user.createdAt)}</td>
                    <td>${this.formatDate(user.lastLoginAt)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary"
                                    onclick="userManagement.editUser('${userId}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger"
                                    onclick="userManagement.deleteUser('${userId}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        // 绑定复选框事件
        this.bindCheckboxEvents();
    }

    renderPagination(data) {
        const pagination = document.querySelector('.pagination');
        if (!pagination) return;

        const totalPages = data.pages || 1;
        const current = data.current || 1;

        let paginationHtml = '';

        // 上一页
        if (current > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="userManagement.goToPage(${current - 1})">上一页</a>
                </li>
            `;
        }

        // 页码
        for (let i = Math.max(1, current - 2); i <= Math.min(totalPages, current + 2); i++) {
            paginationHtml += `
                <li class="page-item ${i === current ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="userManagement.goToPage(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页
        if (current < totalPages) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="userManagement.goToPage(${current + 1})">下一页</a>
                </li>
            `;
        }

        pagination.innerHTML = paginationHtml;
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadUsers();
    }

    bindCheckboxEvents() {
        const checkboxes = document.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const userId = IdUtils.ensureString(e.target.value);
                if (e.target.checked) {
                    this.selectedUsers.add(userId);
                } else {
                    this.selectedUsers.delete(userId);
                }
                this.updateBatchDeleteButton();
                this.updateSelectAllCheckbox();
            });
        });
    }

    updateBatchDeleteButton() {
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');
        if (batchDeleteBtn) {
            batchDeleteBtn.disabled = this.selectedUsers.size === 0;
        }
    }

    updateSelectAllCheckbox() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.user-checkbox');
        
        if (selectAll && checkboxes.length > 0) {
            const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
            selectAll.checked = checkedCount === checkboxes.length;
            selectAll.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
        }
    }

    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const userId = IdUtils.ensureString(checkbox.value);
            if (checked) {
                this.selectedUsers.add(userId);
            } else {
                this.selectedUsers.delete(userId);
            }
        });
        this.updateBatchDeleteButton();
    }

    // 工具方法
    getAvatarUrl(avatar) {
        if (!avatar) return '/images/default-avatar.png';
        if (avatar.startsWith('http')) return avatar;
        return `/avatars/${avatar}`;
    }

    getRoleText(role) {
        const roleMap = {
            1: '<span class="badge bg-danger">管理员</span>',
            2: '<span class="badge bg-primary">普通用户</span>',
            3: '<span class="badge bg-success">教师</span>'
        };
        return roleMap[role] || '<span class="badge bg-secondary">未知</span>';
    }

    getStatusBadge(status) {
        return status === 1 
            ? '<span class="badge bg-success">正常</span>'
            : '<span class="badge bg-danger">禁用</span>';
    }

    formatDate(dateStr) {
        if (!dateStr) return '-';
        try {
            return new Date(dateStr).toLocaleString('zh-CN');
        } catch (error) {
            return '-';
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showError(message) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: '错误',
                text: message
            });
        } else {
            alert(message);
        }
    }

    showSuccess(message) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'success',
                title: '成功',
                text: message,
                timer: 2000,
                showConfirmButton: false
            });
        } else {
            alert(message);
        }
    }
}

// 全局函数
function showAddUserModal() {
    const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
    modal.show();
}

function exportUsers() {
    // 导出用户功能
    console.log('导出用户功能待实现');
}

function importUsers() {
    // 导入用户功能
    console.log('导入用户功能待实现');
}

// 初始化
let userManagement;
document.addEventListener('DOMContentLoaded', function() {
    userManagement = new UserManagement();
});
