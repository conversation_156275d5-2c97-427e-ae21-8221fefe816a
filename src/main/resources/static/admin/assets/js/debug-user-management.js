/**
 * 用户管理调试脚本
 * 用于测试API连接和数据获取
 */

// 调试函数：测试用户列表API
async function debugUserListAPI() {
    console.log('=== 开始测试用户列表API ===');
    
    try {
        const url = '/api/admin/users?page=1&size=10';
        console.log('请求URL:', url);
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('响应状态:', response.status, response.statusText);
        console.log('响应头:', [...response.headers.entries()]);
        
        if (response.ok) {
            const data = await response.json();
            console.log('响应数据:', data);
            
            if (data.success) {
                console.log('✅ API调用成功');
                console.log('用户数量:', data.data ? data.data.length : 0);
                console.log('总数:', data.total);
                console.log('当前页:', data.current);
                console.log('总页数:', data.pages);
            } else {
                console.log('❌ API返回失败:', data.message);
            }
        } else {
            console.log('❌ HTTP请求失败:', response.status, response.statusText);
            const text = await response.text();
            console.log('错误响应内容:', text);
        }
    } catch (error) {
        console.error('❌ 请求异常:', error);
    }
    
    console.log('=== 用户列表API测试结束 ===\n');
}

// 调试函数：测试用户统计API
async function debugUserStatsAPI() {
    console.log('=== 开始测试用户统计API ===');
    
    try {
        const url = '/api/admin/users/stats';
        console.log('请求URL:', url);
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('响应状态:', response.status, response.statusText);
        
        if (response.ok) {
            const data = await response.json();
            console.log('响应数据:', data);
            
            if (data.success) {
                console.log('✅ API调用成功');
                console.log('统计数据:', data.data);
            } else {
                console.log('❌ API返回失败:', data.message);
            }
        } else {
            console.log('❌ HTTP请求失败:', response.status, response.statusText);
            const text = await response.text();
            console.log('错误响应内容:', text);
        }
    } catch (error) {
        console.error('❌ 请求异常:', error);
    }
    
    console.log('=== 用户统计API测试结束 ===\n');
}

// 调试函数：检查DOM元素
function debugDOMElements() {
    console.log('=== 开始检查DOM元素 ===');
    
    const elements = [
        'userTableBody',
        'totalUsers',
        'activeUsers', 
        'disabledUsers',
        'adminUsers',
        'searchInput',
        'roleFilter',
        'statusFilter'
    ];
    
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            console.log(`✅ 找到元素: ${id}`, element);
        } else {
            console.log(`❌ 未找到元素: ${id}`);
        }
    });
    
    console.log('=== DOM元素检查结束 ===\n');
}

// 调试函数：检查UserManagement对象
function debugUserManagementObject() {
    console.log('=== 开始检查UserManagement对象 ===');
    
    if (typeof UserManagement !== 'undefined') {
        console.log('✅ UserManagement类已定义');
    } else {
        console.log('❌ UserManagement类未定义');
    }
    
    if (typeof userManagement !== 'undefined' && userManagement) {
        console.log('✅ userManagement实例已创建:', userManagement);
        console.log('实例方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(userManagement)));
    } else {
        console.log('❌ userManagement实例未创建');
    }
    
    if (typeof window.userManagement !== 'undefined' && window.userManagement) {
        console.log('✅ window.userManagement已设置');
    } else {
        console.log('❌ window.userManagement未设置');
    }
    
    console.log('=== UserManagement对象检查结束 ===\n');
}

// 综合调试函数
async function runAllDebugTests() {
    console.log('🔍 开始运行所有调试测试...\n');
    
    // 检查DOM元素
    debugDOMElements();
    
    // 检查UserManagement对象
    debugUserManagementObject();
    
    // 测试API
    await debugUserStatsAPI();
    await debugUserListAPI();
    
    console.log('🎉 所有调试测试完成！');
}

// 页面加载完成后自动运行调试
document.addEventListener('DOMContentLoaded', function() {
    console.log('调试脚本已加载，可以在控制台运行以下命令：');
    console.log('- runAllDebugTests() : 运行所有调试测试');
    console.log('- debugUserListAPI() : 测试用户列表API');
    console.log('- debugUserStatsAPI() : 测试用户统计API');
    console.log('- debugDOMElements() : 检查DOM元素');
    console.log('- debugUserManagementObject() : 检查UserManagement对象');
    
    // 将函数暴露到全局作用域
    window.runAllDebugTests = runAllDebugTests;
    window.debugUserListAPI = debugUserListAPI;
    window.debugUserStatsAPI = debugUserStatsAPI;
    window.debugDOMElements = debugDOMElements;
    window.debugUserManagementObject = debugUserManagementObject;
});
