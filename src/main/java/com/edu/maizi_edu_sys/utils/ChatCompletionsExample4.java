package com.edu.maizi_edu_sys.utils;

import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import java.util.ArrayList;
import java.util.List;

public class ChatCompletionsExample4 {
    public static void main(String[] args) {
        String apiKey = "fa68bb2a-84f4-48a6-8100-3c11d60f902a"; //System.getenv("ARK_API_KEY");
        ArkService service = ArkService.builder()
                .apiKey(apiKey)
                .build();
        // 第一次请求
        final List<ChatMessage> messages1 = new ArrayList<>();
        final ChatMessage userMessage1 = ChatMessage.builder().role(ChatMessageRole.USER).content("今天天气不错？").build();
        messages1.add(userMessage1);
        ChatCompletionRequest chatCompletionRequest1 = ChatCompletionRequest.builder()
                .model("doubao-seed-1-6-250615") //替换为Model ID，请从文档获取 https://www.volcengine.com/docs/82379/1330310
                .messages(messages1)
                .build();
        service.createChatCompletion(chatCompletionRequest1).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));
        // 第二次请求
        final List<ChatMessage> messages2 = new ArrayList<>();
        final ChatMessage userMessage2 = ChatMessage.builder().role(ChatMessageRole.USER).content("你好").build();
        messages2.add(userMessage2);
        ChatCompletionRequest chatCompletionRequest2 = ChatCompletionRequest.builder()
                .model("doubao-seed-1-6-250615") //替换为Model ID，请从文档获取 https://www.volcengine.com/docs/82379/1330310
                .messages(messages2)
                .build();
        service.createChatCompletion(chatCompletionRequest2).getChoices().forEach(choice -> System.out.println(choice.getMessage().getContent()));
        // shutdown service
        service.shutdownExecutor();
    }
}