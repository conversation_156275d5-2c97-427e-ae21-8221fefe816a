package com.edu.maizi_edu_sys.runner;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 项目启动后执行的任务
 */
@Slf4j
@Component
public class StartupRunner implements ApplicationRunner {
    
    @Value("${server.port:8081}")
    private String serverPort;
    
    @Value("${server.servlet.context-path:}")
    private String contextPath;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            String hostAddress = InetAddress.getLocalHost().getHostAddress();
            String baseUrl = "http://" + hostAddress + ":" + serverPort + contextPath;
            String localhostUrl = "http://localhost:" + serverPort + contextPath;
        } catch (UnknownHostException e) {
            log.error("获取主机地址失败: {}", e.getMessage());
        }
    }
} 