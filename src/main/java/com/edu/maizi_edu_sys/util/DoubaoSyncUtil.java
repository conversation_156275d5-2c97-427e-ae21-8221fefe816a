package com.edu.maizi_edu_sys.util;

import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;

/**
 * 豆包大模型工具类
 * <p>
 * 封装了对豆包大模型API的调用，提供了同步和异步两种方式获取响应结果。
 * 异步方法返回 CompletableFuture，便于在响应完全结束后执行后续处理。
 * </p>
 */
@Component
public class DoubaoSyncUtil {

    @Value("${doubao.api.key:${ARK_API_KEY:}}")
    private String apiKey;

    @Value("${doubao.api.endpoint-id}")
    private String endpointId;

    private ArkService service;

    @PostConstruct
    public void init() {
        if (apiKey == null || apiKey.isEmpty()) {
            throw new IllegalArgumentException("API key for Doubao service is not configured. Please set 'doubao.api.key' in application.yml or set the 'ARK_API_KEY' environment variable.");
        }
        this.service = new ArkService(apiKey);
    }

    /**
     * 发送同步请求到豆包大模型（带超时控制）。
     * <p>
     * 这是一个阻塞方法，会等待模型返回完整响应，但有超时保护。
     * </p>
     *
     * @param content 用户请求的内容
     * @param timeoutSeconds 超时时间（秒），默认30秒
     * @return 模型的完整响应文本
     * @throws RuntimeException 当请求超时或发生错误时
     */
    public String syncRequest(String content, int timeoutSeconds) {
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
            try {
                ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(content).build();
                ChatCompletionRequest request = ChatCompletionRequest.builder()
                        .model(endpointId)
                        .messages(Collections.singletonList(userMessage))
                        .build();

                StringBuilder responseBuilder = new StringBuilder();
                service.createChatCompletion(request).getChoices()
                        .forEach(choice -> responseBuilder.append(choice.getMessage().getContent()));
                return responseBuilder.toString();
            } catch (Exception e) {
                throw new RuntimeException("AI服务调用失败: " + e.getMessage(), e);
            }
        });
        
        try {
            return future.get(timeoutSeconds, java.util.concurrent.TimeUnit.SECONDS);
        } catch (java.util.concurrent.TimeoutException e) {
            future.cancel(true);
            throw new RuntimeException("AI服务调用超时(" + timeoutSeconds + "秒)", e);
        } catch (Exception e) {
            throw new RuntimeException("AI服务调用异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 发送同步请求到豆包大模型（使用默认30秒超时）。
     * 
     * @param content 用户请求的内容
     * @return 模型的完整响应文本
     */
    public String syncRequest(String content) {
        return syncRequest(content, 180);
    }

    /**
     * 发送异步流式请求到豆包大模型。
     * <p>
     * 此方法立即返回一个 CompletableFuture，允许调用者继续执行其他任务。
     * 当模型响应完全结束后，CompletableFuture 会完成并包含最终的完整结果。
     * </p>
     * <pre>{@code
     * CompletableFuture<String> future = doubaoSyncUtil.asyncStreamRequest("介绍一下Java");
     * future.thenAccept(fullResponse -> {
     *     System.out.println("模型响应完成，完整结果: " + fullResponse);
     *     // 在这里执行后续处理
     * });
     * // 主线程可以继续执行其他任务
     * }</pre>
     *
     * @param content 用户请求的内容
     * @return 一个 CompletableFuture，它将在响应完成时包含完整的响应文本
     */
    public CompletableFuture<String> asyncStreamRequest(String content) {
        CompletableFuture<String> future = new CompletableFuture<>();
        StringBuilder responseBuilder = new StringBuilder();

        ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(content).build();
        ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(endpointId)
                .messages(Collections.singletonList(userMessage))
                .stream(true) // 确保是流式请求
                .build();

        try {
            service.streamChatCompletion(request)
                    .doOnComplete(() -> {
                        // 当流结束时，CompletableFuture 完成
                        String result = responseBuilder.toString();
                        if (result.isEmpty()) {
                            future.completeExceptionally(new RuntimeException("AI返回内容为空"));
                        } else {
                            future.complete(result);
                        }
                    })
                    .doOnError(throwable -> {
                        future.completeExceptionally(new RuntimeException("AI API调用失败: " + throwable.getMessage(), throwable));
                    })
                    .subscribe(chunk -> {
                        try {
                            if (chunk.getChoices() != null && !chunk.getChoices().isEmpty() 
                                && chunk.getChoices().get(0).getMessage() != null) {
                                Object chunkContentObj = chunk.getChoices().get(0).getMessage().getContent();
                                if (chunkContentObj != null) {
                                    String chunkContent = chunkContentObj.toString(); // 安全的类型转换
                                    responseBuilder.append(chunkContent);
                                }
                            }
                        } catch (Exception e) {
                            // 对单个chunk的处理异常不应该导致整个流失败，只记录警告
                            System.err.println("处理AI响应chunk时发生异常: " + e.getMessage());
                        }
                    }, throwable -> {
                        // 流处理异常
                        future.completeExceptionally(new RuntimeException("AI流式响应处理失败: " + throwable.getMessage(), throwable));
                    });
        } catch (Exception e) {
            future.completeExceptionally(new RuntimeException("创建AI流式请求失败: " + e.getMessage(), e));
        }

        return future;
    }

    @PreDestroy
    public void shutdown() {
        if (service != null) {
            service.shutdownExecutor();
        }
    }

    public static void main(String[] args) {
        DoubaoSyncUtil doubaoSyncUtil = new DoubaoSyncUtil();

        // 手动设置配置值（模拟Spring的@Value注入）
        try {
            Field apiKeyField = DoubaoSyncUtil.class.getDeclaredField("apiKey");
            apiKeyField.setAccessible(true);
            apiKeyField.set(doubaoSyncUtil, "fa68bb2a-84f4-48a6-8100-3c11d60f902a");

            Field endpointIdField = DoubaoSyncUtil.class.getDeclaredField("endpointId");
            endpointIdField.setAccessible(true);
            endpointIdField.set(doubaoSyncUtil, "doubao-seed-1.6-250615");

            // 手动调用初始化方法（模拟@PostConstruct）
            doubaoSyncUtil.init();

            // 现在可以正常调用
            String result = doubaoSyncUtil.syncRequest("你现在是什么模型");
            System.out.println("AI响应: " + result);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
